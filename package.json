{"name": "erosnowuni_web", "version": "0.1.0", "private": true, "dependencies": {"@reduxjs/toolkit": "^2.8.2", "@tanstack/react-query": "^5.81.2", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "autoprefixer": "^10.4.21", "axios": "^1.10.0", "postcss": "^8.5.6", "react": "^18.3.1", "react-device-detect": "^2.2.3", "react-dom": "^18.3.1", "react-icons": "^5.5.0", "react-redux": "^9.2.0", "react-router-dom": "^7.6.2", "react-scripts": "5.0.1", "styled-components": "^6.1.19", "tailwindcss": "^3.4.17", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"], "rules": {"no-unused-vars": "warn", "no-duplicate-imports": "warn"}}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"prettier": "^3.5.3", "webpack": "^5.99.9", "webpack-cli": "^6.0.1", "webpack-dev-server": "^5.2.2"}}