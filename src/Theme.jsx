import { ThemeProvider } from 'styled-components'
import GlobalStyles from './styles'

const theme = {
    colors: {
        powderWhite: '#FFFDF9',
        persianGreen: '#06B49A',
        lightBlue: '#AFDBD2',
        onyx: '#36313D',
        background: '#191c22',
        black: '#181c23',
        Blue: '#00B8F8',
        Black: '#050505',
        Ash: '#CCCCCC',
        Smoke: '#333333',
        White: '#FFFFFF',
        Green: '#5CB617'
    },
    fonts: {
        fontFamily: 'Poppins, sans-serif',
        fontSizes: {
            fontSize28: '28px',
            small: '1em',
            medium: '2em',
            large: '3em'
        },
        fontWeight: {
            fontWeight600: '600'
        },
        lineHeight: {
            lineHeight34: '34px'
        }
    }
}

const Theme = ({ children }) => (
    <ThemeProvider theme={theme}>
        <GlobalStyles />
        {children}
    </ThemeProvider>
)

export default Theme
