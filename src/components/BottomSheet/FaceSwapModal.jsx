import { useEffect, useRef, useState } from 'react';
import { IoClose } from 'react-icons/io5';

/**
 * FaceSwapModal - A customizable modal component
 * @param {object} props
 * @param {boolean} props.open - Whether the modal is open
 * @param {function} props.onClose - Function to close the modal
 * @param {string} [props.title] - Modal title
 * @param {string} [props.description] - Modal description
 * @param {Array} [props.actions] - Array of action card objects: { icon, title, description, onClick, color }
 * @param {React.ReactNode} [props.children] - Custom content to render instead of actions
 */
const FaceSwapModal = ({ open, onClose, title, description, actions, children }) => {
    const [isClosing, setIsClosing] = useState(false);
    const [dragY, setDragY] = useState(0);
    const [isDragging, setIsDragging] = useState(false);
    const modalRef = useRef(null);
    const startY = useRef(0);
    const currentY = useRef(0);

    useEffect(() => {
        if (!open) {
            setIsClosing(false);
            setDragY(0);
        }
    }, [open]);

    const handleClose = (fromDrag = false) => {
        setIsClosing(true);
        if (!fromDrag) {
            setDragY(0);
        }
        setTimeout(() => {
            onClose();
        }, 300);
    };

    const handleDragStart = (e) => {
        setIsDragging(true);
        startY.current =
            e.type === 'mousedown' ? e.clientY : e.touches[0].clientY;
        currentY.current = startY.current;
    };

    const handleDragMove = (e) => {
        if (!isDragging) return;
        e.preventDefault();
        const clientY =
            e.type === 'mousemove' ? e.clientY : e.touches[0].clientY;
        const deltaY = clientY - startY.current;
        if (deltaY > 0) {
            setDragY(deltaY);
            currentY.current = clientY;
        }
    };

    const handleDragEnd = () => {
        if (!isDragging) return;
        setIsDragging(false);
        if (dragY > 100) {
            handleClose(true);
        } else {
            setDragY(0);
        }
    };

    useEffect(() => {
        if (isDragging) {
            const handleMouseMove = (e) => handleDragMove(e);
            const handleMouseUp = () => handleDragEnd();
            const handleTouchMove = (e) => handleDragMove(e);
            const handleTouchEnd = () => handleDragEnd();

            document.addEventListener('mousemove', handleMouseMove);
            document.addEventListener('mouseup', handleMouseUp);
            document.addEventListener('touchmove', handleTouchMove, {
                passive: false
            });
            document.addEventListener('touchend', handleTouchEnd);

            return () => {
                document.removeEventListener('mousemove', handleMouseMove);
                document.removeEventListener('mouseup', handleMouseUp);
                document.removeEventListener('touchmove', handleTouchMove);
                document.removeEventListener('touchend', handleTouchEnd);
            };
        }
    }, [isDragging, dragY]);

    if (!open) return null;

    return (
        <div
            className={`fixed inset-0 z-50 flex items-end justify-center bg-black transition-opacity duration-300 ${
                isClosing ? 'bg-opacity-0' : 'bg-opacity-50'
            }`}
            onClick={handleClose}
        >
            <div
                ref={modalRef}
                className={`relative w-full max-w-md mx-auto rounded-t-2xl bg-black p-6 pb-8 shadow-lg border-x-2 border-t-2 border-[#00B8F8] ${
                    isClosing ? '' : 'animate-slideInUp'
                }`}
                style={{
                    minHeight: '300px',
                    transform: isClosing
                        ? `translateY(calc(100% + ${dragY}px))`
                        : `translateY(${dragY}px)`,
                    transition: isDragging
                        ? 'none'
                        : 'transform 0.3s cubic-bezier(0.4,0,0.2,1)'
                }}
                onClick={(e) => e.stopPropagation()}
            >
                {/* Drag handle */}
                <div
                    className="flex justify-center mb-4 cursor-grab active:cursor-grabbing"
                    onMouseDown={handleDragStart}
                    onTouchStart={handleDragStart}
                >
                    <div className="w-10 h-1 bg-gray-700 rounded-full" />
                </div>
                {/* Close button */}
                <button
                    className="absolute top-4 right-4 text-gray-400 hover:text-white text-2xl"
                    onClick={handleClose}
                    aria-label="Close"
                >
                    <IoClose />
                </button>
                {/* Title & Description */}
                {(title || description) && (
                    <div className="mt-2 mb-6">
                        {title && (
                            <h2 className="text-[#CAC4D0] text-2xl mb-1 text-start">
                                {title}
                            </h2>
                        )}
                        {description && (
                            <p className="text-[#CAC4D0] text-sm text-start">
                                {description}
                            </p>
                        )}
                    </div>
                )}
                {/* Action Cards or Custom Content */}
                {actions && actions.length > 0 ? (
                    <div className="flex gap-2">
                        {actions.map((action, idx) => (
                            <div
                                key={idx}
                                className="flex-1 min-h-32 border rounded-xl p-4 flex flex-col items-start"
                                style={{ borderColor: action.color || '#00B8F8' }}
                                onClick={action.onClick}
                            >
                                <div className="flex gap-2 items-center">
                                    <div
                                        className="p-2 rounded-lg mb-2"
                                        style={{ background: (action.color || '#00B8F8') + '1A' }}
                                    >
                                        {action.icon}
                                    </div>
                                    <div className="font-semibold text-white mb-1 text-start">
                                        {action.title}
                                    </div>
                                </div>
                                <div
                                    className="text-xs text-start mt-auto"
                                    style={{ color: action.color || '#00B8F8' }}
                                >
                                    {action.description}
                                </div>
                            </div>
                        ))}
                    </div>
                ) : (
                    children
                )}
            </div>
            {/* Animation keyframes */}
            <style>{`
        .animate-slideInUp {
          animation: slideInUp 0.3s cubic-bezier(0.4,0,0.2,1);
        }
        @keyframes slideInUp {
          from { transform: translateY(100%); }
          to { transform: translateY(0); }
        }
      `}</style>
        </div>
    );
};

export default FaceSwapModal; 