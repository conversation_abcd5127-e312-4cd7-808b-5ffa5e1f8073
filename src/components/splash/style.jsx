import styled from 'styled-components'
// import { universe } from '../../assets'
import { landingImage } from '../../assets'

export const SplashContainer = styled.div(({ theme }) => ({
    minHeight: '100vh',
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    // justifyContent: 'center',
    background: theme.colors.Black,
    backgroundImage: `url(${landingImage})`,
    backgroundSize: 'cover',
    backgroundRepeat: 'no-repeat'
}))

export const SplashImage = styled.img`
    width: 11.4375em;
    height: 4.5em;
    max-width: 90vw;
    margin-bottom: 32px;
    margin-left: 10%;
`

export const LogoContainer = styled.div(({ theme }) => ({
    position: 'absolute',
    marginTop: '341px'
}))

export const VideoBox = styled.div`
    width: 100%;
    height: 100vh;
    background: #050505;
    opacity: 64%;
`

export const VideoPlayer = styled.video`
    width: 100%;
    height: 100vh;
    border-radius: 16px;
    background: #050505;
`
