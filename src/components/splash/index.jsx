import {
    SplashContainer,
    SplashImage,
    LogoContainer,
    VideoPlayer,
    VideoBox
} from './style'
import { universe } from '../../assets'
import { Text, Button } from '../../components'

const Splash = ({ onEnter }) => {
    return (
        <SplashContainer>
            <VideoBox>
                {/* <VideoPlayer
                    src={
                        'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4'
                    }
                    controls
                /> */}
            </VideoBox>
            <LogoContainer>
                <SplashImage src={universe} alt="Eros Universe Logo" />
                <Text as="h1" marginBottom={'32px'} fontSize={'28px'}>
                    Ready to create?
                </Text>
                <Button height="40px" onClick={onEnter}>
                    ENTER
                </Button>
            </LogoContainer>
            <Text splashSubtitle gradientText fontSize={'14px'}>
                Unleash Your Creativity
            </Text>
        </SplashContainer>
    )
}

export default Splash
