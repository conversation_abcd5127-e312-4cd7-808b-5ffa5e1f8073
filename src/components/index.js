export { default as <PERSON><PERSON> } from './Button/index'
export { default as InputBox } from './InputBox/index'
export { default as Splash } from './splash'
export { default as Header } from './Header'
export { default as MobileHeader } from './Header/MobileHeader'
export { default as TabBar } from './Tabs'
export { default as MobileTabBar } from './TabBar/MobileTabBar'
export { default as ScrollToTop } from './ScrollToTop/index'
export { default as Text } from './Text/index'
// export { default as MobileHeader } from '../pages/SingleFaceSwap/mobile/MobileHeader'
// export { default as SuggestedList } from '../pages/SingleFaceSwap/mobile/SuggestedList'
// export { default as PopularMoviesList } from '../pages/SingleFaceSwap/mobile/PopularMoviesList'
export { default as Modal } from './Modal'
export { default as Bottomsheet } from './BottomSheet/FaceSwapModal'
export { default as HomePageRails } from './HomePageRails/index'
export { default as PopularRailCard } from './Card/PopularRailCard'
export { default as SuggestedRailCard } from './Card/SuggestedRailCard'
export { default as PopularMoviesList } from './Rails/PopularMoviesList'
export { default as SuggestedList } from './Rails/SuggestedList'