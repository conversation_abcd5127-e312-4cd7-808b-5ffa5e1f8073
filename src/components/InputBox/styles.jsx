import styled from 'styled-components'

export const InputWrapper = styled.div`
    // margin-bottom: 2vh;
    width: 100%;
`

export const Label = styled.label`
    display: block;
    margin-bottom: 5px;
    color: #fff;
    font-size: 14px;
    text-align: left;
`

export const StyledInput = styled.input`
    width: calc(100% - 20px);
    padding: 10px;
    border-radius: 5px;
    border: 1px solid ${(props) => (props.$error ? '#ff7a7a' : '#ccc')};
    background-color: transparent;
    color: #cccccc;
    font-size: 14px;
    &:focus {
        outline: none;
        border-color: #007bff;
    }
    &[type='date']::-webkit-calendar-picker-indicator {
        filter: invert(1);
        cursor: pointer;
    }
`
