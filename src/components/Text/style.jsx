// export const MyText = (as = 'p') =>
//     styled(as)((props) => ({
//         position: props?.position || '',
//         fontSize: props?.size || props?.theme.fonts.fontSize?.FH28,
//         color: props?.color || props?.theme.colors?.White,
//         fontWeight: props?.weight || props?.theme.fonts.fontWeight.FW600,
//         textAlign: props?.align || 'left',
//         lineHeight: props?.lineHeight || props?.theme.fonts.lineHeight.LH34,
//         bottom: props?.bottom || '',
//         marginTop: props?.marginTop || ''
//     }))

import styled, { css } from 'styled-components'

const TextWrapper = styled.span`
    font-size: ${({ fontSize }) => fontSize || 'px'};
    color: ${({ color }) => color || 'white'};
    width: ${({ width }) => width || 'px'};
    font-weight: ${({ fontWeight }) => fontWeight || '400'};
    margin: ${({ margin }) => margin || ''};
    margin-top: ${({ marginTop }) => marginTop || ''};
    margin-bottom: ${({ marginBottom }) => marginBottom || ''};

    ${({ splashSubtitle }) =>
        splashSubtitle &&
        css`
            font-size: 24px;
            font-weight: 600;
            position: absolute;
            bottom: 7%;
            background: linear-gradient(90deg, #5cb617, #00b8f8);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;

            /* For Firefox (optional): */
            background-clip: text;
            color: transparent;
        `};
    ${({ gradientText }) =>
        gradientText &&
        css`
            background: linear-gradient(90deg, #5cb617, #00b8f8);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;

            /* For Firefox (optional): */
            background-clip: text;
            color: transparent;
            text-align: center;
        `};
    ${({ setMargin }) =>
        setMargin &&
        css`
            margin: auto;
        `};
`

export { TextWrapper }
