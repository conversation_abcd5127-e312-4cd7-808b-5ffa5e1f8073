// const Text = ({ as = 'p', children, className, ...rest }) => {
//     const StyledText = MyText(as)
//     return (
//         <StyledText className={className} {...rest}>
//             {children}
//         </StyledText>
//     )
// }

import { TextWrapper } from './style'

const Text = ({
    textalign,
    marginTop,
    marginBottom,
    text,
    fromchannels,
    tag = 'span',
    color,
    children,
    fromsettingpage,
    fromMultiModal,
    textDecoration,
    ml,
    fontSize,
    margin,
    ...rest
}) => {
    return (
        <TextWrapper
            fromchannels={fromchannels}
            as={tag}
            color={color}
            textalign={textalign}
            marginTop={marginTop}
            marginBottom={marginBottom}
            fromsettingpage={fromsettingpage}
            fromMultiModal={fromMultiModal}
            ml={ml}
            textDecoration={textDecoration}
            fontSize={fontSize}
            margin={margin}
            {...rest}
        >
            {children || text}
        </TextWrapper>
    )
}

export default Text
