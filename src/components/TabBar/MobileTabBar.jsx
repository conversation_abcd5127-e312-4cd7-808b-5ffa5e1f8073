import { channel, create, discover, home, store } from '../../assets'
import { useLocation, useNavigate } from 'react-router-dom'

const tabs = [
    { key: 'home', label: 'Home', icon: home, path: '/' },
    { key: 'discover', label: 'Discover', icon: discover, path: '/discover' },
    { key: 'create', label: 'Create', icon: create, path: '/create' },
    { key: 'store', label: 'Store', icon: store, path: '/store' },
    { key: 'channel', label: 'Channel', icon: channel, path: '/channel' }
]

const themeBlue = '#00B8F8'

const MobileTabBar = () => {
    const navigate = useNavigate()
    const location = useLocation()

    return (
        <div className="fixed left-0 right-0 bottom-0 bg-[#050505] border-t border-[#23243a] flex justify-around items-center h-[60px] z-50 pt-2">
            {tabs.map((tab) => {
                const isActive = location.pathname === tab.path || (tab.key === 'home' && location.pathname === '/') || (tab.key === 'create' && location.pathname === '/create')
                return (
                    <div
                        key={tab.key}
                        className={`flex flex-col items-center text-xs font-medium cursor-pointer`}
                        style={{ color: isActive ? themeBlue : 'white' }}
                        onClick={() => navigate(tab.path)}
                    >
                        <span className="w-full h-full">
                            <img src={tab.icon} alt={tab.label} className="object-cover w-full h-full" style={{
    filter: isActive
      ? 'brightness(0) saturate(100%) invert(62%) sepia(98%) saturate(749%) hue-rotate(163deg) brightness(102%) contrast(101%)'
      : 'none'
  }} />
                        </span>
                    </div>
                )
            })}
        </div>
    )
}

export default MobileTabBar
