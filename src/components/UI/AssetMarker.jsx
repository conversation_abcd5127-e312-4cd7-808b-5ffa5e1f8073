import { photoMarker, videoMarker } from '../../assets'

import { CONTENT_TYPE } from '../../utils/constants'

// Marks image or video cards by adding an icon on the top right corner
// Parent div must have class of relative
const AssetMarker = ({ content_type }) => {
    return (
        <>
            <div className="absolute top-2 right-2">
                <div className="absolute inset-0 bg-black bg-opacity-20 rounded-full blur-sm scale-150 pointer-events-none"></div>
                <img
                    height={24}
                    width={24}
                    src={
                        content_type === CONTENT_TYPE.VIDEO
                            ? videoMarker
                            : photoMarker
                    }
                    className="relative brightness-0 invert"
                />
            </div>
        </>
    )
}

export default AssetMarker
