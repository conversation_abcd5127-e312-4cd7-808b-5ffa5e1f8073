import { useEffect, useState } from 'react'

import { FaTimes } from 'react-icons/fa'

const Toast = ({ 
    message, 
    type = 'error', 
    show = false, 
    onClose, 
    duration = 5000,
    position = 'bottom'
}) => {
    const [isVisible, setIsVisible] = useState(show)

    useEffect(() => {
        setIsVisible(show)
    }, [show])

    useEffect(() => {
        if (isVisible && duration > 0) {
            const timer = setTimeout(() => {
                handleClose()
            }, duration)

            return () => clearTimeout(timer)
        }
    }, [isVisible, duration])

    const handleClose = () => {
        setIsVisible(false)
        if (onClose) {
            onClose()
        }
    }

    if (!isVisible || !message) return null

    const getToastStyles = () => {
        const baseStyles = "fixed left-1/2 transform -translate-x-1/2 z-50 transition-all duration-300 ease-in-out"
        const positionStyles = position === 'top' ? 'top-4' : 'bottom-4'
        return `${baseStyles} ${positionStyles}`
    }

    const getTypeStyles = () => {
        switch (type) {
            case 'success':
                return 'bg-green-600 border-green-500'
            case 'warning':
                return 'bg-yellow-600 border-yellow-500'
            case 'info':
                return 'bg-blue-600 border-blue-500'
            case 'error':
            default:
                return 'bg-[#E27D7D] border-[#E27D7D]'
        }
    }

    return (
        <div className={getToastStyles()}>
            <div className={`
                ${getTypeStyles()}
                text-white 
                px-6 py-4 
                rounded-lg 
                shadow-lg 
                border-l-4 
                flex items-center justify-between 
                min-w-80 max-w-md
                animate-slide-up
            `}>
                <div className="flex items-center">
                    <span className="text-sm font-medium">
                        {message}
                    </span>
                </div>
                
                <button
                    onClick={handleClose}
                    className="ml-4 text-white hover:text-gray-200 transition-colors duration-200 flex-shrink-0"
                    aria-label="Close toast"
                >
                    <FaTimes size={14} />
                </button>
            </div>
        </div>
    )
}

export default Toast
