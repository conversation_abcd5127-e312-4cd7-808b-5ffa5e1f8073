const Loader = ({ text = 'Loading...', show = true }) => {
    if (!show) return null

    return (
        <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50">
            <div className="bg-[#23243a] rounded-lg p-8 flex flex-col items-center shadow-lg border border-gray-600">
                {/* Spinner */}
                <div className="w-12 h-12 border-4 border-gray-600 border-t-[#03586A] rounded-full animate-spin mb-4"></div>

                {/* Text */}
                <p className="text-white text-lg font-medium text-center">
                    {text}
                </p>
            </div>
        </div>
    )
}

export default Loader
