import React, { createContext, useCallback, useContext, useState } from 'react'

import Toast from './Toast'

const ToastContext = createContext()

export const useToast = () => {
    const context = useContext(ToastContext)
    if (!context) {
        throw new Error('useToast must be used within a ToastProvider')
    }
    return context
}

export const ToastProvider = ({ children }) => {
    const [toasts, setToasts] = useState([])

    const addToast = useCallback((message, type = 'error', duration = 5000) => {
        // Ensure message is always a string
        let toastMessage = 'An error occurred';

        if (typeof message === 'string') {
            toastMessage = message;
        } else if (message && typeof message === 'object') {
            if (message.detail) {
                toastMessage = message.detail;
            } else if (message.message) {
                toastMessage = message.message;
            } else {
                toastMessage = JSON.stringify(message);
            }
        } else if (message) {
            toastMessage = String(message);
        }

        const id = Date.now() + Math.random()
        const newToast = {
            id,
            message: toastMessage,
            type,
            duration,
            show: true
        }

        setToasts(prev => [...prev, newToast])

        // Auto remove toast after duration
        if (duration > 0) {
            setTimeout(() => {
                removeToast(id)
            }, duration)
        }

        return id
    }, [])

    const removeToast = useCallback((id) => {
        setToasts(prev => prev.filter(toast => toast.id !== id))
    }, [])

    const showError = useCallback((message, duration) => {
        return addToast(message, 'error', duration)
    }, [addToast])

    const showSuccess = useCallback((message, duration) => {
        return addToast(message, 'success', duration)
    }, [addToast])

    const showWarning = useCallback((message, duration) => {
        return addToast(message, 'warning', duration)
    }, [addToast])

    const showInfo = useCallback((message, duration) => {
        return addToast(message, 'info', duration)
    }, [addToast])

    const clearAll = useCallback(() => {
        setToasts([])
    }, [])

    const value = {
        showError,
        showSuccess,
        showWarning,
        showInfo,
        clearAll,
        addToast,
        removeToast
    }

    return (
        <ToastContext.Provider value={value}>
            {children}
            
            {/* Render toasts */}
            <div className="fixed bottom-0 left-0 right-0 z-50 pointer-events-none">
                <div className="flex flex-col items-center space-y-2 pb-4">
                    {toasts.map((toast, index) => (
                        <div key={toast.id} className="pointer-events-auto">
                            <Toast
                                message={toast.message}
                                type={toast.type}
                                show={toast.show}
                                duration={0} // Disable auto-close since we handle it in provider
                                onClose={() => removeToast(toast.id)}
                                position="bottom"
                            />
                        </div>
                    ))}
                </div>
            </div>
        </ToastContext.Provider>
    )
}
