// import { priyanka, singleFaceSwap } from '../../../assets'

import SuggestedRailCard from '../Card/SuggestedRailCard'

// const suggested = [
//     { id: 1, img: priyanka, alt: 'Suggested 1' },
//     { id: 2, img: singleFaceSwap, alt: 'Suggested 2' },
//     { id: 3, img: singleFaceSwap, alt: 'Suggested 3' }
// ]

const SuggestedList = ({ title, data }) => (
    <div className="mb-6">
        <div className="text-start font-semibold text-xl mb-4">
            {title}
        </div>
        <div className="flex gap-3 overflow-x-auto flex-nowrap hide-scrollbar">
            {data?.map((item) => (
                <SuggestedRailCard item={item} key={item.id} />
            ))}
        </div>
    </div>
)

export default SuggestedList
