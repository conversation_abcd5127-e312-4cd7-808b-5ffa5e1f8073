import PopularRailCard from '../Card/PopularRailCard'

const PopularMoviesList = ({ title, data }) => (
    <div className="mb-6">
        <div className="flex items-center mb-4">
            <div className="font-semibold text-xl">{title}</div>
            <a
                href="#"
                className="text-[#2ec6ff] text-sm ml-auto font-semibold"
            >
                View All &rarr;
            </a>
        </div>
        <div className="flex gap-3 overflow-x-auto flex-nowrap hide-scrollbar">
            {data?.map((item) => (
                <PopularRailCard item={item} key={item.id} />
            ))}
        </div>
    </div>
)

export default PopularMoviesList
