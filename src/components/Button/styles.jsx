import styled from 'styled-components'

export const StyledButton = styled.button((props) => ({
    height: props?.height || '',
    backgroundColor: props?.theme.colors.Blue,
    color: props?.color || props.theme.colors.White,
    padding: '10px 20px',
    borderRadius: '100px',
    border: 'none',
    cursor: 'pointer',
    fontSize: props.size,
    fontWeight: props.theme.fonts.fontWeight.FW600,
    lineHeight: props.lineHeight,
    '&:hover': {
        backgroundColor: props?.theme.colors.Blue
    },
    marginTop: props?.mt || '',
    marginBottom: props?.mb || ''
}))
