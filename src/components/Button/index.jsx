import React from 'react'
import { StyledButton } from './styles'

const Button = ({ children, disabled, style, ...props }) => {
    const disabledStyle = disabled
        ? {
            background: '#05485E',
            color: '#7A8C98',
            borderRadius: 24,
            cursor: 'not-allowed',
            ...style
        }
        : style;
    return <StyledButton disabled={disabled} style={disabledStyle} {...props}>{children}</StyledButton>
}

export default Button
