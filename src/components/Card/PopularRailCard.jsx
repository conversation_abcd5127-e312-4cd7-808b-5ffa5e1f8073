import {
    resetGenerationSession,
    updateGenerationSession
} from '../../redux/Reducers/generationSessionReducer'

import AssetMarker from '../UI/AssetMarker'
import { CONTENT_TYPE } from '../../utils/constants'
import { useDispatch } from 'react-redux'
import { useNavigate } from 'react-router-dom'

const PopularRailCard = ({ item }) => {
    const navigate = useNavigate()
    const dispatch = useDispatch()

    const handleCardClick = () => {
        const is_CharacterCard = !!item?.character_name
        const is_Video = item?.content_type === CONTENT_TYPE.VIDEO
        const template_id = item.content_type === CONTENT_TYPE.VIDEO ? item.video_template_id : item.image_template_id;

        dispatch(resetGenerationSession())
        dispatch(
            updateGenerationSession({
                template_id,
                thumbnail_url: item.thumbnail ?? item.image_url,
                file_url: item.file_url ?? item.image_url,
                content_type: item.content_type ?? CONTENT_TYPE.IMAGE
            })
        )

        if (is_Video) navigate('/source')
        else navigate(`/gallery/${item.title ?? item.character_name}`)
    }

    return (
        <div
            key={item.id}
            className="w-32 h-44 flex-shrink-0 rounded-2xl bg-[#23243a] shadow-md overflow-hidden border border-gray-100 border-opacity-20 relative"
            onClick={handleCardClick}
        >
            <img
                src={item.thumbnail ?? item.image_url}
                alt={item.title ?? item.character_name}
                className="w-full h-full object-cover"
            />
            <div className="absolute bottom-0 left-0 right-0 h-1/2 bg-gradient-to-t from-black/65 to-transparent pointer-events-none" />
            <div className="text-xs absolute bottom-4 left-4 right-4 z-10 text-start">
                {item.title ?? item.character_name}
            </div>

            <AssetMarker content_type={item.content_type ?? CONTENT_TYPE.IMAGE} />
        </div>
    )
}

export default PopularRailCard
