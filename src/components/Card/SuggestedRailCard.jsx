import {
    resetGenerationSession,
    updateGenerationSession
} from '../../redux/Reducers/generationSessionReducer'

import AssetMarker from '../UI/AssetMarker'
import { CONTENT_TYPE } from '../../utils/constants'
import { useDispatch } from 'react-redux'
import { useNavigate } from 'react-router-dom'

const SuggestedRailCard = ({ item }) => {
    const navigate = useNavigate()
    const dispatch = useDispatch()

    const handleCardClick = () => {
        console.log(`You Tapped: ${JSON.stringify(item)}`);
        
        const template_id = item.content_type === CONTENT_TYPE.VIDEO ? item.video_template_id : item.image_template_id;
        
        dispatch(resetGenerationSession())
        dispatch(
            updateGenerationSession({
                template_id,
                thumbnail_url: item.thumbnail ?? item.file_url,
                file_url: item.file_url,
                content_type: item.content_type
            })
        )
        navigate(`/source`)
    }

    return (
        <div
            key={item.template_id}
            className="w-44 h-44 flex-shrink-0 rounded-2xl bg-[#23243a] shadow-md overflow-hidden border border-gray-100 border-opacity-20 relative"
            onClick={handleCardClick}
        >
            <img
                src={item.thumbnail ?? item.file_url}
                alt={`${item.title} - ${item.description}`}
                className="w-full h-full object-cover"
            />

            {/* image or video tag with overlay */}
            <AssetMarker content_type={item.content_type} />
        </div>
    )
}

export default SuggestedRailCard
