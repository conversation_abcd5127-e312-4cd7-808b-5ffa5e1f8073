const DoubleRow = (props) => {
    console.log('DoubleRow props', props)
    const [data1, data2] = props.data
    return (
        <div
            style={{
                display: 'flex',
                flexDirection: 'column-reverse',
                gap: '10px'
            }}
        >
            <div
                key={data1?.id}
                style={{
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center'
                }}
            >
                <img
                    src={data1?.file_url}
                    alt={data1?.title}
                    style={{
                        width: '150px',
                        height: 'auto',
                        maxWidth: '150px'
                    }}
                />
                <div style={{ textAlign: 'center' }}>{data1?.title}</div>
            </div>
            <div
                key={data2?.id}
                style={{
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center'
                }}
            >
                <img
                    src={data2?.file_url}
                    alt={data2?.title}
                    style={{
                        width: '150px',
                        height: 'auto',
                        maxWidth: '150px'
                    }}
                />
                <div style={{ textAlign: 'center' }}>{data2?.title}</div>
            </div>
        </div>
    )
}

export default DoubleRow
