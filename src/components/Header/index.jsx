import React from 'react'
import { useSelector } from 'react-redux'
import { useNavigate } from 'react-router-dom'
import {
    HeaderContainer,
    Logo,
    Credits,
    UserInfo,
    Avatar,
    UserDetails,
    UserName,
    UserHandle,
    LogoImage,
    IconWrapper,
    SubIcon,
    avatarMargin
} from './styles'
import { universe, search, bell, face } from '../../assets'

const Header = ({ credits }) => {
    const user = useSelector((state) => state.auth.user)
    const isAuthenticated = useSelector((state) => state.auth.isAuthenticated)
    const navigate = useNavigate()

    // You can set a default avatar if needed
    const defaultAvatar = universe

    const handleLogoClick = () => {
        navigate('/')
    }

    return (
        <HeaderContainer>
            <Logo onClick={handleLogoClick} style={{ cursor: 'pointer' }}>
                <LogoImage src={universe} alt="Eros Universe Logo" />
            </Logo>
            <IconWrapper>
                {/* <Credits>{credits} Credits</Credits> */}
                <SubIcon src={search} alt="search" />
                <SubIcon src={bell} alt="notification" />
                <SubIcon src={face} avatarMargin alt="profile photo" />
                {/* {isAuthenticated && user && (
                    <UserInfo>
                        <Avatar src={defaultAvatar} alt={user.username} />
                        <UserDetails>
                            <UserName>{user.username}</UserName>
                            <UserHandle>{user.email}</UserHandle>
                        </UserDetails>
                    </UserInfo>
                )} */}
            </IconWrapper>
        </HeaderContainer>
    )
}

export default Header
