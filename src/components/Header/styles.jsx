import styled, { css } from 'styled-components'

export const HeaderContainer = styled.div`
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: ${({ theme }) => theme.colors.Black};
    padding: 10px 18px;
    height: 64px;
`

export const Logo = styled.div`
    display: flex;
    align-items: center;
`

export const LogoImage = styled.img`
    width: 81.28px;
    height: 32px;
    top: 68px;
    left: 15.86px;
`

export const Credits = styled.div`
    color: #fff;
    font-size: 1.1rem;
    margin-right: 0;
    white-space: nowrap;
`

export const UserInfo = styled.div`
    display: flex;
    align-items: center;
    // background: #191c22;
    // border-radius: 12px;
    // padding: 0px 12px;
    // gap: 12px;
`

export const Avatar = styled.img`
    width: 25px;
    height: 25px;
    border-radius: 50%;
    object-fit: cover;
`

export const UserDetails = styled.div`
    display: flex;
    flex-direction: column;
`

export const UserName = styled.div`
    color: #fff;
    font-size: 0.9rem;
    font-weight: 500;
`

export const UserHandle = styled.div`
    color: #bdbdbd;
    font-size: 0.7rem;
`

export const IconWrapper = styled.div`
    display: flex;
    align-items: center;
`
export const SubIcon = styled.img`
    ${({ avatarMargin }) =>
        avatarMargin &&
        css`
            margin-left: 10px;
        `};
`
