import { useDispatch, useSelector } from 'react-redux'
import { useNavigate } from 'react-router-dom'
import { TABS } from '../../utils/constants'
import { setActiveTab } from '../../redux/Reducers/uiReducer'

const TabBar = () => {
    const dispatch = useDispatch()
    const navigate = useNavigate()
    const activeTab = useSelector((state) => state.ui.activeTab)

    const handleTabClick = (tab) => {
        dispatch(setActiveTab(tab.label))
        navigate(tab.path)
    }

    return (
        <div
            style={{
                display: 'flex',
                gap: 18,
                margin: 32,
                justifyContent: 'center'
            }}
        >
            {TABS.map((tab) => (
                <div
                    key={tab.label}
                    style={{
                        background:
                            activeTab === tab.label
                                ? 'linear-gradient(90deg, #3ad1c7 0%, #2ec6ff 100%)'
                                : '#23243a',
                        color: activeTab === tab.label ? '#23243a' : '#bdbdbd',
                        fontWeight: 600,
                        fontSize: 18,
                        borderRadius: 32,
                        padding: '12px 36px',
                        cursor: 'pointer',
                        border: 'none',
                        outline: 'none',
                        transition: 'background 0.2s'
                    }}
                    onClick={() => handleTabClick(tab)}
                >
                    {tab.label}
                </div>
            ))}
        </div>
    )
}

export default TabBar
