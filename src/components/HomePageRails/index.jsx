import React, { useEffect } from 'react'
import {
    HomePageContainer,
    Rail,
    RailTitle,
    RailCards,
    Card,
    CardImage,
    CardTitle,
    FaceSwapCard,
    TopCreatorsSection,
    SectionTitle,
    CreatorsRow,
    CreatorItem,
    CreatorAvatar,
    CreatorName,
    RailWrapper,
    ExploreRailCards
} from './styles'
import { useSelector } from 'react-redux'
import DoubleRow from '../DoubleRow'

const mockData = [
    {
        // title: 'Trending Now',
        items: Array.from({ length: 10 }).map((_, i) => ({
            id: i,
            title: `Face Swap ${i + 1}`,
            image: `https://via.placeholder.com/150x100?text=Trending+${i + 1}`
        }))
    }
    // {
    //   title: 'Recommended For You',
    //   items: Array.from({ length: 8 }).map((_, i) => ({
    //     id: i,
    //     title: `Recommended ${i + 1}`,
    //     image: `https://via.placeholder.com/150x100?text=Recommended+${i + 1}`,
    //   })),
    // },
    // {
    //   title: 'New Releases',
    //   items: Array.from({ length: 6 }).map((_, i) => ({
    //     id: i,
    //     title: `New ${i + 1}`,
    //     image: `https://via.placeholder.com/150x100?text=New+${i + 1}`,
    //   })),
    // },
    //   {
    //   title: 'Trending Now',
    //   items: Array.from({ length: 10 }).map((_, i) => ({
    //     id: i,
    //     title: `Trending ${i + 1}`,
    //     image: `https://via.placeholder.com/150x100?text=Trending+${i + 1}`,
    //   })),
    // },
    // {
    //   title: 'Recommended For You',
    //   items: Array.from({ length: 8 }).map((_, i) => ({
    //     id: i,
    //     title: `Recommended ${i + 1}`,
    //     image: `https://via.placeholder.com/150x100?text=Recommended+${i + 1}`,
    //   })),
    // },
    // {
    //   title: 'New Releases',
    //   items: Array.from({ length: 6 }).map((_, i) => ({
    //     id: i,
    //     title: `New ${i + 1}`,
    //     image: `https://via.placeholder.com/150x100?text=New+${i + 1}`,
    //   })),
    // },
]

const topCreators = [
    {
        id: 1,
        name: 'PixelRaaga',
        avatar: 'https://i.pravatar.cc/100?img=1'
    },
    {
        id: 2,
        name: 'Dreamfire',
        avatar: 'https://i.pravatar.cc/100?img=2'
    },
    {
        id: 3,
        name: 'RahulRaj',
        avatar: 'https://i.pravatar.cc/100?img=3'
    },
    {
        id: 4,
        name: 'Shadow',
        avatar: 'https://i.pravatar.cc/100?img=4'
    },
    {
        id: 5,
        name: 'PixelRaaga',
        avatar: 'https://i.pravatar.cc/100?img=1'
    },
    {
        id: 6,
        name: 'Dreamfire',
        avatar: 'https://i.pravatar.cc/100?img=2'
    },
    {
        id: 7,
        name: 'RahulRaj',
        avatar: 'https://i.pravatar.cc/100?img=3'
    },
    {
        id: 8,
        name: 'Shadow',
        avatar: 'https://i.pravatar.cc/100?img=4'
    }
]

const HomePageRails = () => {
    let doubledata = []
    const highlightData = useSelector((state) => state?.homeData?.user?.data)
    useEffect(() => {
        console.log('highlightData', highlightData)
    }, [highlightData])
    return (
        <HomePageContainer>
            {mockData.map((rail, index) => (
                <Rail key={index}>
                    <RailTitle>{rail.title}</RailTitle>
                    <RailCards>
                        {rail.items.map((item) => (
                            <FaceSwapCard key={item.id}>
                                {/* <CardImage src={item.image} alt={item.title} /> */}
                                <CardTitle>{item.title}</CardTitle>
                            </FaceSwapCard>
                        ))}
                    </RailCards>
                </Rail>
            ))}

            <RailTitle>Today's Highlights</RailTitle>
            <RailCards>
                {highlightData?.["Today's highlights"].map((item, index) => {
                    console.log('item xxxx', item)
                    console.log('index xxxx', index)

                    if (index % 3 === 0) {
                        doubledata = []
                        return (
                            <Card
                                key={item.id}
                                onClick={() =>
                                    console.log(`Clicked on ${item.title}`)
                                }
                            >
                                <CardImage
                                    src={item.file_url}
                                    alt={item.title}
                                />
                                <CardTitle>{item.title}</CardTitle>
                            </Card>
                        )
                    } else {
                        if (index % 3 === 1) doubledata.push(item)
                        else {
                            doubledata.push(item)
                            return (
                                <DoubleRow
                                    data={doubledata}
                                    index={index}
                                    key={item.id}
                                />
                            )
                        }
                    }
                })}
            </RailCards>

            <TopCreatorsSection>
                <SectionTitle>Top Creators</SectionTitle>
                <CreatorsRow>
                    {topCreators.map((creator) => (
                        <CreatorItem key={creator.id}>
                            <CreatorAvatar
                                src={creator.avatar}
                                alt={creator.name}
                            />
                            <CreatorName>{creator.name}</CreatorName>
                        </CreatorItem>
                    ))}
                </CreatorsRow>
            </TopCreatorsSection>
            <RailTitle>Explore</RailTitle>
            <ExploreRailCards>
                {Object.keys(highlightData?.Explore || {}).map(
                    (rail, index) => {
                        if (index % 4 !== 0 && index % 4 !== 1) {
                            doubledata = []
                            return (
                                <Card
                                    key={highlightData?.Explore[rail][0].id}
                                    onClick={() =>
                                        console.log(
                                            `Clicked on ${highlightData?.Explore[rail][0].title}`
                                        )
                                    }
                                >
                                    <CardImage
                                        src={
                                            highlightData?.Explore[rail][0]
                                                .file_url
                                        }
                                        alt={
                                            highlightData?.Explore[rail][0]
                                                .title
                                        }
                                    />
                                    <CardTitle>
                                        {highlightData?.Explore[rail][0].title}
                                    </CardTitle>
                                </Card>
                            )
                        } else {
                            if (index % 4 === 0) {
                                doubledata.push(highlightData?.Explore[rail][0])
                            }
                            if (index % 4 === 1) {
                                doubledata.push(highlightData?.Explore[rail][0])
                                return (
                                    <DoubleRow
                                        data={doubledata}
                                        index={index}
                                        key={rail}
                                    />
                                )
                            }
                        }
                    }
                )}
            </ExploreRailCards>
        </HomePageContainer>
    )
}

export default HomePageRails
