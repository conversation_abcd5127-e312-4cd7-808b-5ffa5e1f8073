import styled from 'styled-components';

export const HomePageContainer = styled.div`
  padding: 20px;
`;

export const Rail = styled.div`
  // margin-bottom: 40px;
`;

export const RailWrapper = styled.div`
  display: flex;
  width: 100%;
`;
export const RailTitle = styled.h2`
  text-align: left;
  font-size: 20px;
  margin-bottom: 12px;
  color: white;
`;

export const RailCards = styled.div`
  display: flex;
  overflow-x: auto;
  gap: 16px;
  padding-bottom: 10px;

  &::-webkit-scrollbar {
    height: 8px;
    display: none; 
  }

  &::-webkit-scrollbar-thumb {
    background-color: #aaa;
    border-radius: 4px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }
`;

export const ExploreRailCards = styled.div`
  display: flex;
  width: 412px;
  flex-wrap: wrap;
  gap: 16px;
  padding-bottom: 10px;

  &::-webkit-scrollbar {
    height: 8px;
    display: none; 
  }

  &::-webkit-scrollbar-thumb {
    background-color: #aaa;
    border-radius: 4px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }
`;

export const Card = styled.div`
  width: 186px;
  height: 380px;
  flex: 0 0 auto;
  background-color: white;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0px 2px 5px rgba(0,0,0,0.1);
`;

export const FaceSwapCard = styled.div`
  width: 186px;
  height: 120px;
  flex: 0 0 auto;
  background-color: white;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0px 2px 5px rgba(0,0,0,0.1);
  display: flex;
  flex-direction: column-reverse;
`;

export const CardImage = styled.img`
 object-fit: cover;
  width: 100%;
  height: 100% !important;
  height: auto;
  display: block;
`;

export const CardTitle = styled.p`
  font-size: 14px;
  padding: 8px;
  text-align: center;
  color: #444;
`;

export const TopCreatorsSection = styled.div`
  padding: 0px 0px;
`;

export const SectionTitle = styled.h3`
  font-size: 20px;
  color: white;
  font-weight: 600;
  margin-bottom: 12px;
  text-align: left;
`;

export const CreatorsRow = styled.div`
  display: flex;
  gap: 16px;
  overflow-x: auto;
  padding-bottom: 8px;
  &::-webkit-scrollbar {
    display: none; 
  }
`;

export const CreatorItem = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 70px;
`;

export const CreatorAvatar = styled.img`
  width: 60px;
  height: 60px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid white;
`;

export const CreatorName = styled.span`
  margin-top: 8px;
  font-size: 12px;
  text-align: center;
  color: white;
`;

export const StackCard = styled.div`
  width: 140px;
  height: 200px;
  border-radius: 12px;
  overflow: hidden;
  background-color: #222;
  display: flex;
  flex-direction: column;
`;

export const StackCardImage = styled.img`
  width: 100%;
  height: 50%;
  object-fit: cover;
`;

