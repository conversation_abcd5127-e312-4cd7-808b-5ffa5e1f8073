import React from 'react';
import { backdropStyle, modalStyle, titleStyle, buttonStyle } from './style';

export default function Modal({ open, onClose, title, children, okButtonStyle }) {
  if (!open) return null;
  return (
    <div style={backdropStyle}>
      <div style={modalStyle}>
        {title && <div style={titleStyle}>{title}</div>}
        <div>{children}</div>
        <button style={okButtonStyle ? { ...buttonStyle, ...okButtonStyle } : buttonStyle} onClick={onClose}>OK</button>
      </div>
    </div>
  );
} 