import {
    fetchFSVideoStatus,
    fetchFaceSwapTemplateInfo,
    fetchFaceSwapTemplates,
    swapFace,
    uploadFaceSwapTargets,
    uploadFaceSwapTemplate
} from '../api/faceSwapApi'
import {
    fetchFaceSwapVideoInfo,
    startFaceSwap,
    uploadNewFace,
    uploadVideoForFaceSwap
} from '../api/videoFaceSwapApi'
import { useMutation, useQuery } from '@tanstack/react-query'

import { CONTENT_TYPE } from '../utils/constants'

// 1. Get All Templates
export const useGetAllFSTemplates = (params = {}, options = {}) => {
    return useQuery({
        queryKey: ['GET_FS_TEMPLATES'],
        queryFn: async () => {
            const resp = await fetchFaceSwapTemplates()
            return resp
        },
        initialData: [],
        retry: true,
        ...options
    })
}

// 2. Get Template Info
export const useGetFSTemplateInfo = (params = {}, options = {}) => {
    const { template_id, content_type } = params

    return useQuery({
        queryKey: ['GET_FS_TEMPLATE_INFO', template_id, content_type],
        queryFn: async () => {
            let resp
            if (content_type === CONTENT_TYPE.VIDEO) {
                resp = await fetchFaceSwapVideoInfo(template_id)
            } else {
                resp = await fetchFaceSwapTemplateInfo(template_id)
            }

            return resp
        },
        enabled: !!template_id && !!content_type,
        initialData: [],
        retry: true,
        ...options
    })
}

// 3. Upload Template
export const useUploadFSTemplate = (params = {}, options = {}) => {
    return useMutation({
        mutationKey: ['UPLOAD_FS_TEMPLATE'],
        mutationFn: async ({ data, content_type }) => {
            try {
                let resp
                if (content_type === CONTENT_TYPE.VIDEO) {
                    resp = await uploadVideoForFaceSwap(data)
                } else {
                    resp = await uploadFaceSwapTemplate(data)
                }

                return resp
            } catch (error) {
                // Ensure we always throw a proper Error instance
                console.error('Upload template error:', error)

                let errorMessage =
                    'Failed to upload template. Please try again.'

                if (error instanceof Error) {
                    errorMessage = error.message
                } else if (typeof error === 'string') {
                    errorMessage = error
                } else if (error?.detail) {
                    errorMessage = error.detail
                } else if (error?.message) {
                    errorMessage = error.message
                } else if (error?.response?.data?.detail) {
                    errorMessage = error.response.data.detail
                }

                // Handle specific error cases
                if (errorMessage.includes('User credit balance not found')) {
                    errorMessage =
                        'Insufficient credits. Please check your credit balance.'
                }

                throw new Error(errorMessage)
            }
        },
        ...options
    })
}

// 4. Upload Target Face uploadNewFace
export const useUploadFSTarget = (params = {}, options = {}) => {
    return useMutation({
        mutationKey: ['UPLOAD_FS_TARGET'],
        mutationFn: async (data) => {
            try {
                let resp
                if (data?.content_type === CONTENT_TYPE.VIDEO) {
                    resp = await uploadNewFace(
                        data?.generation_id,
                        data?.group_id,
                        data?.formData
                    )
                } else {
                    resp = await uploadFaceSwapTargets(data?.formData)
                }
                return resp
            } catch (error) {
                // Ensure we always throw a proper Error instance
                console.error('Upload template error:', error)

                let errorMessage =
                    'Failed to upload template. Please try again.'

                if (error instanceof Error) {
                    errorMessage = error.message
                } else if (typeof error === 'string') {
                    errorMessage = error
                } else if (error?.detail) {
                    errorMessage = error.detail
                } else if (error?.message) {
                    errorMessage = error.message
                } else if (error?.response?.data?.detail) {
                    errorMessage = error.response.data.detail
                }

                // Handle specific error cases
                if (errorMessage.includes('User credit balance not found')) {
                    errorMessage =
                        'Insufficient credits. Please check your credit balance.'
                }

                throw new Error(errorMessage)
            }
        },
        ...options
    })
}

// 5. Face Swap
export const useFaceSwap = (params = {}, options = {}) => {
    return useMutation({
        mutationKey: ['FACE_SWAP'],
        mutationFn: async (data) => {
            let resp
            if (data?.content_type === CONTENT_TYPE.VIDEO) {
                // data is generation_id
                resp = await startFaceSwap(data?.generation_id)
            } else {
                resp = await swapFace(data)
            }
            return resp
        },
        ...options
    })
}

export const useGetFSVideoStatus = (params = {}, options = {}) => {
    const { generation_id, shouldFetchVideoStatus } = params

    return useQuery({
        queryKey: ['GET_FS_VIDEO_STATUS', generation_id, shouldFetchVideoStatus],
        queryFn: async () => {
            try {
                const resp = await fetchFSVideoStatus(generation_id)
                return resp;
            } catch (error) {
                throw new Error(error)
            }
        },
        enabled: shouldFetchVideoStatus,
        retry: true,
        ...options
    })
}