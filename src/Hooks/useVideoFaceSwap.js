import { useMutation, useQuery } from '@tanstack/react-query'
import { uploadVideoForFaceSwap, uploadNewFace, startFaceSwap, getFaceSwapStatus } from '../api/videoFaceSwapApi'

export const useUploadVideoForFaceSwap = ( params = {} , options = {} ) => {
    return useMutation({
        mutationKey: ['UPLOAD_VIDEO_FOR_FACE_SWAP'],
        mutationFn: async ({ template_id , user_id }) =>{
            const resp = await uploadVideoForFaceSwap( template_id , user_id )
            return resp
        },
        ...options
    })
}

export const useUploadNewFace = ( params = {} , options = {} ) => {
    return useMutation({
        mutationKey: ['UPLOAD_NEW_FACE'],
        mutationFn: async ({ generation_id , group_id , file }) =>{
            const resp = await uploadNewFace( generation_id , group_id , file )
            return resp
        },
        ...options
    })
}

export const useStartFaceSwap = ( params = {} , options = {} ) => {
    return useMutation({
        mutationKey: ['START_FACE_SWAP'],
        mutationFn: async (generation_id) =>{
            const resp = await startFaceSwap( generation_id )
            return resp
        },
        ...options
    })
}

export const useGetFaceSwapStatus = ( params = {} , options = {} ) => {
    const { generationId, shouldGetVideoUrl } = params
    return useQuery({
        queryKey: ['GET_FACE_SWAP_STATUS', generationId, shouldGetVideoUrl],
        queryFn: async () =>{
            const resp = await getFaceSwapStatus( generationId )
            return resp
        },
        enabled: !!shouldGetVideoUrl,
        ...options
    })
}
