import { fetchContentMeta, fetchCreatePageData } from '../api/createPageApi'

import { useQuery } from '@tanstack/react-query'

export const useGetCreatePageData = (params = {}, options = {}) => {
    return useQuery({ 
        queryKey: ['GET_CREATE_PAGE'],
        queryFn: async () => {
            const resp = await fetchCreatePageData()
            return resp;
        },
        ...options
    })
}

export const useGetContentMeta = (params = {}, options = {}) => {
    const { query } = params
    const urlEncodedQuery = encodeURIComponent(query)
    return useQuery({
        queryKey: ['GET_CONTENT_META', query],
        queryFn: async () => {
            const resp = await fetchContentMeta(query)
            return resp
        },
        enabled: !!query,
        ...options
    })
}