import React, { useState } from 'react'
import { use<PERSON><PERSON><PERSON>, Link } from 'react-router-dom'
import {
    MainContainer,
    LeftPane,
    RightPane,
    FormCard,
    Logo,
    Separator,
    FormSection,
    PasswordRow,
    CheckboxWrapper,
    TermsText,
    LoginLinkWrapper
} from './styles'
import { Button, InputBox } from '../../components'
import { loginPageImg, ErosuniverseImg, universe } from '../../assets'
import { registerAPI } from '../../api/loginApi'
import { isMobile } from 'react-device-detect'
import Text from '../../components/Text'
import photoAdd from '../../assets/images/photo-add.svg'

function Register() {
    const [form, setForm] = useState({
        username: '',
        email: '',
        contact_number: '',
        password: '',
        dob: ''
    })
    const [status, setStatus] = useState('idle')
    const [error, setError] = useState(null)
    const navigate = useNavigate()
    const [dobFocused, setDobFocused] = useState(false)
    const [profileImage, setProfileImage] = useState(null)
    const fileInputRef = React.useRef(null)

    const handleChange = (e) => {
        setForm({ ...form, [e.target.name]: e.target.value })
    }

    const handleImageClick = () => {
        if (fileInputRef.current) fileInputRef.current.click()
    }

    const handleImageChange = (e) => {
        if (e.target.files && e.target.files[0]) {
            setProfileImage(e.target.files[0])
        }
    }

    const handleRegister = async (e) => {
        e.preventDefault()
        if (status === 'loading') return // Prevent double submit
        setStatus('loading')
        setError(null)
        try {
            const response = await registerAPI(form)
            console.log('Registration API response:', response)
            if (
                response &&
                response.message &&
                response.message.toLowerCase().includes('verification email')
            ) {
                setStatus('success')
                navigate('/login')
            } else {
                setStatus('idle')
                setError('Registration failed')
            }
        } catch (err) {
            setStatus('idle')
            setError(err?.response?.data?.detail || 'Registration failed')
        }
    }

    if (isMobile) {
        // Check if all fields are filled (trimmed, and dob must not be empty)
        //const allFieldsFilled = [form.username, form.email, form.contact_number, form.password, form.dob].every(v => v && v.toString().trim() !== '');
        return (
            <div className="min-h-screen bg-black flex flex-col items-center px-4 pt-8">
                <img
                    src={universe}
                    alt="Eros Universe Logo"
                    style={{ width: '32vw', marginTop: '5vh' }}
                />
                <h2 style={{ margin: '2vh' }}>Create New Account</h2>
                <Text
                    color="#ccc"
                    align="center"
                    lineHeight="3vh"
                    style={{ marginBottom: '2vh' }}
                >
                    Lets get started!
                    <br />
                    Enter your details below
                </Text>
                <div className="flex flex-col items-center w-full">
                    <button
                        type="button"
                        onClick={handleImageClick}
                        style={{
                            width: '23vw',
                            height: '23vw',
                            borderRadius: '50%',
                            marginBottom: '2vh',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            overflow: 'hidden',
                            background: '#222',
                            border: 'none',
                            position: 'relative',
                            marginLeft: '40vw'
                        }}
                    >
                        {profileImage ? (
                            <img
                                src={URL.createObjectURL(profileImage)}
                                alt="Profile Preview"
                                style={{
                                    width: '100%',
                                    height: '100%',
                                    objectFit: 'cover'
                                }}
                            />
                        ) : (
                            <img src={photoAdd} alt="Add" className="w-8 h-8" />
                        )}
                        <input
                            type="file"
                            accept="image/*"
                            ref={fileInputRef}
                            style={{ display: 'none' }}
                            onChange={handleImageChange}
                        />
                    </button>
                    <form
                        onSubmit={handleRegister}
                        className="w-full flex flex-col gap-4"
                    >
                        <InputBox
                            placeholder="Name"
                            type="text"
                            name="username"
                            value={form.username}
                            onChange={handleChange}
                            style={{
                                background: 'transparent',
                                border: '1px solid #4b5563',
                                borderRadius: '0.5rem',
                                padding: '0.75rem 1rem',
                                color: 'white',
                                width: '80vw',
                                marginBottom: '0.5rem'
                            }}
                            required
                        />
                        <InputBox
                            placeholder="Email"
                            type="email"
                            name="email"
                            value={form.email}
                            onChange={handleChange}
                            style={{
                                background: 'transparent',
                                border: '1px solid #4b5563',
                                borderRadius: '0.5rem',
                                padding: '0.75rem 1rem',
                                color: 'white',
                                width: '80vw',
                                marginBottom: '0.5rem'
                            }}
                            required
                        />
                        <div
                            style={{
                                marginBottom: '2vh',
                                position: 'relative'
                            }}
                        >
                            <InputBox
                                type="date"
                                name="dob"
                                value={form.dob}
                                onChange={handleChange}
                                onFocus={() => setDobFocused(true)}
                                onBlur={() => setDobFocused(false)}
                                style={{
                                    background: 'transparent',
                                    border: '1px solid #4b5563',
                                    borderRadius: '0.5rem',
                                    padding: '0.75rem 1rem',
                                    color:
                                        dobFocused || form.dob
                                            ? 'white'
                                            : 'black',
                                    width: '80vw',
                                    position: 'relative'
                                }}
                            />
                            {!form.dob && !dobFocused && (
                                <span
                                    style={{
                                        position: 'absolute',
                                        left: '10vw',
                                        top: '3vh',
                                        transform: 'translateY(-50%)',
                                        color: '#6d6969',
                                        pointerEvents: 'none',
                                        fontSize: '16px',
                                        zIndex: 2,
                                        background: 'black',
                                        padding: '1px'
                                    }}
                                >
                                    Date of Birth
                                </span>
                            )}
                        </div>
                        <div className="relative">
                            <InputBox
                                placeholder="Password"
                                type="password"
                                name="password"
                                value={form.password}
                                onChange={handleChange}
                                style={{
                                    background: 'transparent',
                                    border: '1px solid #4b5563',
                                    borderRadius: '0.5rem',
                                    padding: '0.75rem 1rem',
                                    color: 'white',
                                    width: '75vw',
                                    paddingRight: '2.5rem'
                                }}
                                required
                            />
                        </div>
                        {error && (
                            <div className="text-red-500 text-sm text-center">
                                {error}
                            </div>
                        )}
                        <Button
                            type="submit"
                            style={{
                                width: '90vw',
                                padding: 12,
                                borderRadius: 24,
                                margin: '16px 0 0 0',
                                fontSize: 16,
                                fontWeight: 600,
                                background: '#007bff',
                                color: '#fff',
                                cursor:
                                    status !== 'loading'
                                        ? 'pointer'
                                        : 'not-allowed',
                                opacity: status !== 'loading' ? 1 : 0.7
                            }}
                            disabled={status === 'loading'}
                        >
                            {status === 'loading' ? 'Signing Up...' : 'Sign Up'}
                        </Button>
                    </form>
                </div>
                <Text
                    color="#b3b3b3"
                    fontSize={'12px'}
                    align="center"
                    lineHeight="3vh"
                    position="absolute"
                    bottom="2vh"
                    width="62%"
                >
                    By continuing, you agree to our{' '}
                    <a
                        href="#"
                        style={{
                            color: '#38bdf8',
                            textDecoration: 'underline'
                        }}
                    >
                        Terms of Service
                    </a>
                    ,{' '}
                    <a
                        href="#"
                        style={{
                            color: '#38bdf8',
                            textDecoration: 'underline'
                        }}
                    >
                        Privacy Policy
                    </a>{' '}
                    and{' '}
                    <a
                        href="#"
                        style={{
                            color: '#38bdf8',
                            textDecoration: 'underline'
                        }}
                    >
                        Cookie Policy
                    </a>
                </Text>
            </div>
        )
    }

    return (
        <div>
            <MainContainer>
                <LeftPane>
                    <img src={loginPageImg} alt="Abstract Visual" />
                </LeftPane>
                <RightPane>
                    <FormCard>
                        <Logo>
                            <img
                                src={ErosuniverseImg}
                                alt="Eros Universe Logo"
                                style={{ maxWidth: '100%', height: 'auto' }}
                            />
                        </Logo>
                        <Separator>Register</Separator>
                        <FormSection>
                            <form onSubmit={handleRegister}>
                                <InputBox
                                    label="Username"
                                    placeholder="Enter your username"
                                    type="text"
                                    name="username"
                                    value={form.username}
                                    onChange={handleChange}
                                />
                                <InputBox
                                    label="Email"
                                    placeholder="Enter your email"
                                    type="email"
                                    name="email"
                                    value={form.email}
                                    onChange={handleChange}
                                />
                                <InputBox
                                    label="Contact Number"
                                    placeholder="Enter your contact number"
                                    type="text"
                                    name="contact_number"
                                    value={form.contact_number}
                                    onChange={handleChange}
                                />
                                <PasswordRow>
                                    <label htmlFor="password">Password</label>
                                </PasswordRow>
                                <InputBox
                                    placeholder="Enter your password"
                                    type="password"
                                    id="password"
                                    name="password"
                                    value={form.password}
                                    onChange={handleChange}
                                />
                                <CheckboxWrapper>
                                    <input
                                        type="checkbox"
                                        id="terms"
                                        required
                                    />
                                    <TermsText htmlFor="terms">
                                        I agree to the Terms & Privacy
                                    </TermsText>
                                </CheckboxWrapper>
                                {error && (
                                    <div
                                        style={{
                                            color: 'red',
                                            marginBottom: 8
                                        }}
                                    >
                                        {error}
                                    </div>
                                )}
                                <Button
                                    style={{
                                        width: '100%',
                                        padding: '12px 0',
                                        backgroundColor: '#007bff'
                                    }}
                                    type="submit"
                                    disabled={status === 'loading'}
                                >
                                    {status === 'loading'
                                        ? 'Registering...'
                                        : 'Register'}
                                </Button>
                            </form>
                        </FormSection>
                        <LoginLinkWrapper>
                            Already have an account?{' '}
                            <Link to="/login">Login</Link>
                        </LoginLinkWrapper>
                    </FormCard>
                </RightPane>
            </MainContainer>
        </div>
    )
}

export default Register
