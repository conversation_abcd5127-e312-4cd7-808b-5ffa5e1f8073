import styled from 'styled-components'

export const MainContainer = styled.div(({ theme }) => ({
    width: '100vw',
    height: '100vh',
    minHeight: '100vh',
    minWidth: '100vw',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    background: theme.colors.black,
    backgroundImage: `url('/assets/images/BlurGrp.png')`,
    backgroundSize: 'cover',
    backgroundRepeat: 'no-repeat',
    overflow: 'hidden'
}))

export const LeftPane = styled.div`
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }
`

export const RightPane = styled.div`
    flex: 1;
    display: flex;
    justify-content: center;
`

export const FormCard = styled.div`
    padding: 5px 28px;
    display: flex;
    flex-direction: column;
    align-items: center;
`

export const Logo = styled.h1`
    width: 21vw;
`

export const SocialLoginButtons = styled.div`
    display: flex;
    gap: 20px;
    margin-bottom: 5px;

    button {
        padding: 12px 25px;
        border-radius: 8px;
        font-weight: bold;
        min-width: 200px;
    }
`

export const Separator = styled.div`
    margin: 0px 0px 20px 0px;
    color: #aaa;
    position: relative;
    width: 100%;
    text-align: center;

    &::before,
    &::after {
        content: '';
        position: absolute;
        top: 50%;
        width: 40%;
        height: 1px;
        background-color: #444;
    }

    &::before {
        left: 0;
    }

    &::after {
        right: 0;
    }
`

export const SeparatorForMweb = styled.div`
    color: #aaa;
    position: relative;
    width: 75vw;
    text-align: center;

    &::before,
    &::after {
        content: '';
        position: absolute;
        top: 50%;
        width: 40%;
        height: 1px;
        background-color: #444;
    }

    &::before {
        left: 0;
    }

    &::after {
        right: 0;
    }
`

export const FormSection = styled.div`
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 5px;
`

export const PasswordRow = styled.div`
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    margin-bottom: 2px;

    label {
        color: #fff;
        font-size: 16px;
    }
`

export const ForgotPasswordLink = styled.a`
    color: #007bff;
    text-decoration: none;
    font-size: 14px;
    text-align: right;
    margin: 0;
`

export const CheckboxWrapper = styled.div`
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 20px;

    input[type='checkbox'] {
        width: 16px;
        height: 16px;
        background-color: #333;
        border: 1px solid #ccc;
        border-radius: 3px;
        cursor: pointer;
        appearance: none;
        -webkit-appearance: none;
        position: relative;

        &:checked {
            background-color: #007bff;
            border-color: #007bff;

            &::after {
                content: '';
                position: absolute;
                left: 5px;
                top: 2px;
                width: 4px;
                height: 9px;
                border: solid white;
                border-width: 0 2px 2px 0;
                transform: rotate(45deg);
            }
        }
    }
`

export const TermsText = styled.label`
    font-size: 14px;
    color: #ccc;
`

export const LoginLinkWrapper = styled.div`
    margin-top: 30px;
    font-size: 14px;
    color: #ccc;

    a {
        color: #fff;
        text-decoration: none;
        margin-left: 5px;
        font-weight: bold;
    }
`

// Mobile styles
export const MobileLoginContainer = styled.div`
    background: #111;
    color: #fff;
    min-height: 100vh;
    width: 100vw;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
`

export const MobileLogo = styled.img`
    width: 32vw;
    margin: 24px 0 32px 0;
`

export const MobileInput = styled.input`
    width: 75vw;
    max-width: 340px;
    padding: 12px 6px;
    margin: 11px 0;
    border-radius: 6px;
    border: 1px solid #444;
    background: #222;
    color: #fff;
    font-size: 16px;
    outline: none;
`

export const MobileButton = styled.button`
    width: 80vw;
    max-width: 340px;
    padding: 10px;
    border-radius: 24px;
    background: #03586a;
    color: #fff;
    border: none;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    opacity: ${({ disabled }) => (disabled ? 0.6 : 1)};
`

export const MobileButtonLink = styled.button`
    background: none;
    color: ${({ theme }) => theme.colors.Blue};
    border: none;
    margin-top: 10px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    text-decoration: none;
    opacity: ${({ disabled }) => (disabled ? 0.6 : 1)};
`

export const MobileSecondaryButton = styled(MobileButton)`
    width: 74vw;
    background: transparent;
    color: ${({ theme }) => theme.colors.Blue};
    border: 1px solid ${({ theme }) => theme.colors.Blue};
    text-decoration: none;
    // margin: 1vh;
`

export const MobileLink = styled.a`
    color: ${({ theme }) => theme.colors.Blue};
    text-decoration: none;
    font-size: 15px;
    margin: 0;
    display: block;
    text-align: center;
`

export const MobileSocialIcons = styled.div`
    display: flex;
    gap: 8vw;
    margin: 2vh 0 1vh 0;
    justify-content: center;
    gap: 16px;
    margin-bottom: 66px;
    margin-top: 16px;
`

export const MobileSocialIcon = styled.img`
    // width: 32px;
    // height: 32px;
    object-fit: contain;
    
`

export const MobileTermsText = styled.div`
    width: 80vw;
    font-size: 12px;
    color: #aaa;
    position: absolute;
    bottom: 2vh;
    text-align: center;
    a {
        color: ${({ theme }) => theme.colors.Blue};
        text-decoration: underline;
    }
`
