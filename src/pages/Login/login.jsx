import React, { useState, useEffect } from 'react'
import { login, clearError } from '../../redux/Reducers/loginReducer'
import { useDispatch, useSelector } from 'react-redux'
import {
    MainContainer,
    LeftPane,
    RightPane,
    FormCard,
    Logo,
    SocialLoginButtons,
    Separator,
    SeparatorForMweb,
    FormSection,
    PasswordRow,
    ForgotPasswordLink,
    CheckboxWrapper,
    TermsText,
    LoginLinkWrapper,
    // Mobile styles
    MobileLoginContainer,
    MobileLogo,
    // MobileInput,
    // MobileButton,
    MobileLink,
    MobileSocialIcons,
    MobileSocialIcon,
    MobileSecondaryButton
    // MobileButtonLink,
    // MobileTermsText
} from './styles'
import { Button, InputBox } from '../../components'
import { Link, useNavigate } from 'react-router-dom'
import {
    ErosuniverseImg,
    Google,
    Apple,
    universe,
    appleLogo,
    instaLogo,
    googleLogo
} from '../../assets'
import { isMobile } from 'react-device-detect'
import { Text, Modal } from '../../components'

function Login() {
    const [email, setEmail] = useState('')
    const [password, setPassword] = useState('')
    const { status, error } = useSelector((state) => state.auth)
    const isAuthenticated = useSelector((state) => state.auth.isAuthenticated)
    const dispatch = useDispatch()
    const navigate = useNavigate()
    const [showErrorModal, setShowErrorModal] = useState(false)
    const [emailError, setEmailError] = useState('')
    const [passwordError, setPwdError] = useState('')
    const [emailTouched, setEmailTouched] = useState(false)
    const [passwordTouched, setPwdTouched] = useState(false)

    useEffect(() => {
        if (isAuthenticated) {
            navigate('/home')
        }
    }, [isAuthenticated, navigate])

    useEffect(() => {
        console.log('Redux error state:', error)
        if (error && isMobile) setShowErrorModal(true)
    }, [error])

    useEffect(() => {
        console.log('showErrorModal:', showErrorModal)
    }, [showErrorModal])

    // Email validation function
    const validateEmail = (value) => {
        return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)
    }

    const validatePassword = (value) => {
        var passwordRegex =
            /^(?=.*?[A-Z])(?=.*?[a-z])(?=.*?[0-9])(?=.*?[#?!@$%^&*-]).{8,}$/
        return passwordRegex.test(value)
    }
    const handleEmailBlur = () => {
        setEmailTouched(true)
        if (!validateEmail(email)) {
            setEmailError('Enter a valid email address')
        } else {
            setEmailError('')
        }
    }

    const handlePwdBlur = () => {
        setPwdTouched(true)
        if (!validatePassword(password)) {
            // setPwdError('Enter a valid password')
            setPwdError('')
        } else {
            setPwdError('')
        }
    }

    const handleLogin = (e) => {
        e.preventDefault()
        dispatch(login({ email, password }))
            .unwrap()
            .then((data) => {
                localStorage.setItem('access', data.access)
                localStorage.setItem('refresh', data.refresh)
                localStorage.setItem('user', JSON.stringify(data.user))
            })
            .catch(() => {
                // Error is handled by Redux state
            })
    }

    if (isMobile) {
        return (
            <MobileLoginContainer>
                <MobileLogo src={universe} alt="Eros Universe Logo" />
                <Text as="h2" fontSize={'20px'} margin="unset" align="center">
                    Sign In
                </Text>
                <Text
                    fontSize={'16px'}
                    marginTop={'24px'}
                    marginBottom={'6px'}
                    lineHeight="3vh"
                >
                    Hi! Welcome back!
                </Text>
                <Text fontSize={'14px'} color="#CCCCCC" marginBottom={'24px'}>
                    Let's get somehing great done today.
                </Text>
                {/* {showErrorModal && (
                    <div style={{ color: 'red', textAlign: 'center' }}>
                        Popup would show here
                    </div>
                )} */}
                <form onSubmit={handleLogin} style={{ width: '100%' }}>
                    <InputBox
                        type="email"
                        placeholder="Email"
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        onBlur={handleEmailBlur}
                        error={emailTouched && emailError}
                        required
                        style={{
                            width: '75vw',
                            maxWidth: 340,
                            height: '56px',
                            marginBottom: emailError ? '' : ' 36px'
                            // marginBottom: '36px'
                        }}
                    />
                    {emailTouched && emailError && (
                        <div
                            style={{
                                color: '#ff7a7a',
                                fontSize: '14px',
                                marginBottom: 8,
                                textAlign: 'start',
                                marginLeft: '18vw'
                            }}
                        >
                            {emailError}
                        </div>
                    )}
                    <InputBox
                        type="password"
                        placeholder="Password"
                        value={password}
                        onChange={(e) => setPassword(e.target.value)}
                        onBlur={handlePwdBlur}
                        error={passwordTouched && passwordError}
                        required
                        style={{
                            width: '75vw',
                            maxWidth: 340,
                            height: '56px',
                            marginBottom: passwordError ? '' : ' 36px'
                        }}
                    />
                    {passwordTouched && passwordError && (
                        <div
                            style={{
                                color: '#ff7a7a',
                                fontSize: '14px',
                                marginBottom: 8,
                                textAlign: 'start',
                                marginLeft: '18vw'
                            }}
                        >
                            {passwordError}
                        </div>
                    )}
                    <Button
                        type="submit"
                        disabled={
                            status === 'loading' ||
                            !email ||
                            !password ||
                            emailError ||
                            passwordError
                        }
                        style={{
                            width: '80vw',
                            maxWidth: 340,
                            padding: 12,
                            borderRadius: 24,
                            fontSize: 14,
                            fontWeight: 600
                        }}
                    >
                        {status === 'loading' ? 'Signing In...' : 'Sign In'}
                    </Button>
                </form>
                <Modal
                    open={showErrorModal}
                    onClose={() => {
                        setShowErrorModal(false)
                        dispatch(clearError())
                    }}
                    title="Login Failed"
                    okButtonStyle={{
                        display: 'block',
                        marginLeft: 'auto',
                        marginRight: 24,
                        marginTop: 24,
                        background: 'none',
                        color: '#00bfff',
                        border: 'none',
                        // fontWeight: 600,
                        // fontSize: '1rem',
                        cursor: 'pointer',
                        boxShadow: 'none',
                        outline: 'none'
                        // padding: 0
                    }}
                >
                    Please check your email and password and try again.
                </Modal>
                <MobileLink href="#" style={{ margin: '2vh 0' }}>
                    Forgot Password?
                </MobileLink>
                <SeparatorForMweb>or</SeparatorForMweb>
                <MobileSocialIcons>
                    <MobileSocialIcon src={googleLogo} alt="Google" />
                    <MobileSocialIcon src={appleLogo} alt="Apple" />
                    <MobileSocialIcon src={instaLogo} alt="Instagram" />
                </MobileSocialIcons>
                <MobileSecondaryButton as={Link} to="/register">
                    Create New Account
                </MobileSecondaryButton>
                <Button
                    as={Link}
                    to="/home"
                    style={{
                        width: '75vw',
                        maxWidth: 340,
                        background: 'none',
                        color: '#00bfff',
                        border: 'none',
                        marginTop: 10,
                        fontSize: 14,
                        fontWeight: 600,
                        textDecoration: 'none'
                    }}
                >
                    Explore as Guest
                </Button>
                <Text
                    color="#b3b3b3"
                    fontSize={'12px'}
                    align="center"
                    lineHeight="3vh"
                    marginTop="24px"
                    marginBottom="8px"
                    position="absolute"
                    bottom="2vh"
                    width="62%"
                >
                    By continuing, you agree to our{' '}
                    <a
                        href={process.env.PUBLIC_URL + '/terms.html'}
                        style={{
                            color: '#38bdf8',
                            textDecoration: 'underline'
                        }}
                    >
                        Terms of Service
                    </a>
                    ,{' '}
                    <a
                        href={process.env.PUBLIC_URL + '/privacy.html'}
                        style={{
                            color: '#38bdf8',
                            textDecoration: 'underline'
                        }}
                    >
                        Privacy Policy
                    </a>{' '}
                    and{' '}
                    <a
                        href=""
                        style={{
                            color: '#38bdf8',
                            textDecoration: 'underline'
                        }}
                    >
                        Cookie Policy
                    </a>
                </Text>
            </MobileLoginContainer>
        )
    }

    return (
        <div>
            <MainContainer>
                <LeftPane>
                    {/* The abstract image */}
                    <img
                        src={require('../../assets/images/LoginPageImg.png')}
                        alt="Abstract Visual"
                    />
                </LeftPane>
                <RightPane>
                    <FormCard>
                        <Logo>
                            <img
                                src={ErosuniverseImg}
                                alt="Eros Universe Logo"
                                style={{ maxWidth: '100%', height: 'auto' }}
                            />
                        </Logo>
                        <SocialLoginButtons>
                            <Button
                                style={{
                                    backgroundColor: '#fff',
                                    color: '#000',
                                    border: '1px solid #ccc',
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    gap: '10px'
                                }}
                            >
                                <img
                                    src={Google}
                                    alt="Google Logo"
                                    style={{ width: '20px', height: '20px' }}
                                />
                                Login with Google
                            </Button>
                            <Button
                                style={{
                                    backgroundColor: '#fff',
                                    color: '#000',
                                    border: '1px solid #ccc',
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    gap: '10px'
                                }}
                            >
                                <img
                                    src={Apple}
                                    alt="Apple Logo"
                                    style={{ width: '20px', height: '20px' }}
                                />
                                Login with Apple
                            </Button>
                        </SocialLoginButtons>
                        <Separator>or</Separator>
                        <FormSection>
                            <form onSubmit={handleLogin}>
                                <InputBox
                                    label="Email"
                                    placeholder="Enter your email"
                                    type="email"
                                    value={email}
                                    onChange={(e) => setEmail(e.target.value)}
                                    style={{
                                        height: '56px'
                                    }}
                                />
                                <PasswordRow>
                                    <label htmlFor="password">Password</label>
                                    <ForgotPasswordLink href="#">
                                        Forgot Password?
                                    </ForgotPasswordLink>
                                </PasswordRow>
                                <InputBox
                                    placeholder="Enter your password"
                                    type="password"
                                    id="password"
                                    value={password}
                                    onChange={(e) =>
                                        setPassword(e.target.value)
                                    }
                                    style={{
                                        height: '56px'
                                    }}
                                />
                                <CheckboxWrapper>
                                    <input type="checkbox" id="terms" />
                                    <TermsText htmlFor="terms">
                                        I agree to the Terms & Privacy
                                    </TermsText>
                                </CheckboxWrapper>
                                {error && (
                                    <div
                                        style={{
                                            color: 'red',
                                            marginBottom: 8
                                        }}
                                    >
                                        {error}
                                    </div>
                                )}
                                <Button
                                    style={{
                                        width: '100%',
                                        padding: '12px 0',
                                        backgroundColor: '#007bff'
                                    }}
                                    type="submit"
                                    disabled={status === 'loading'}
                                >
                                    {status === 'loading'
                                        ? 'Logging in...'
                                        : 'Login'}
                                </Button>
                            </form>
                        </FormSection>
                        <LoginLinkWrapper>
                            Create new Account{' '}
                            <Link to="/register">Sign UP</Link>
                        </LoginLinkWrapper>
                    </FormCard>
                </RightPane>
            </MainContainer>
        </div>
    )
}

export default Login
