import styled from 'styled-components';

export const PageContainer = styled.div`
  background: #23243a;
  min-height: 100vh;
  color: #fff;
  font-family: 'Inter', sans-serif;
  padding: 24px;
  border-radius: 24px;
  box-shadow: 0 4px 32px #0002;
`;

export const Tabs = styled.div`
  display: flex;
  justify-content: center;
  gap: 16px;
  padding: 32px 0 24px 0;
`;

export const Tab = styled.div`
  padding: 12px 32px;
  border-radius: 32px;
  background: ${({ active }) => (active ? '#3ad1c7' : '#393a54')};
  color: ${({ active }) => (active ? '#23243a' : '#fff')};
  font-weight: 600;
  font-size: 18px;
  cursor: pointer;
  transition: background 0.2s;
`;

export const MainContent = styled.div`
  display: flex;
  justify-content: center;
  align-items: flex-start;
  gap: 40px;
  max-width: 1200px;
  margin: 0 auto;
`;

export const GalleryPanel = styled.div`
  background: #282943;
  border-radius: 16px;
  padding: 24px;
  width: 240px;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
  align-content: flex-start;
`;

export const GalleryTitle = styled.div`
  font-size: 18px;
  font-weight: 700;
  margin-bottom: 16px;
  grid-column: 1 / -1;
`;

export const GalleryItem = styled.div`
  background: ${({ selected }) => (selected ? '#3ad1c7' : '#393a54')};
  border: 2px solid transparent;
  color: ${({ selected }) => (selected ? '#23243a' : '#fff')};
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  font-weight: 700;
  aspect-ratio: 1 / 1;

  &:hover {
    border-color: #3ad1c7;
  }
`;

export const VideoPanel = styled.div`
  flex: 1;
  background: #282943;
  border-radius: 16px;
  padding: 24px 32px;
  min-width: 500px;
`;

export const SectionTitle = styled.div`
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 18px;
  text-align: center;
`;

export const VideoBox = styled.div`
  width: 100%;
  height: 320px;
  background: #393a54;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 24px;
`;

export const VideoPlayer = styled.video`
  width: 100%;
  height: 100%;
  border-radius: 16px;
  background: #191a2a;
`;

export const Placeholder = styled.div`
  color: #bdbdbd;
  font-size: 18px;
`;

export const FaceSwapSection = styled.div`
  margin-top: 24px;
  display: flex;
  flex-direction: column;
  align-items: center;
`;

export const FaceUploaders = styled.div`
  display: flex;
  align-items: center;
  gap: 32px;
  margin-bottom: 16px;
`;

export const FaceCircle = styled.div`
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: #393a54;
  border: 2px dashed #3ad1c7;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36px;
  color: #3ad1c7;
  cursor: pointer;
  overflow: hidden;
  opacity: ${({ disabled }) => (disabled ? 0.5 : 1)};
  pointer-events: ${({ disabled }) => (disabled ? 'none' : 'auto')};
  & > img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
  }
`;

export const Arrow = styled.div`
  font-size: 32px;
  color: #fff;
`;

export const AgreeRow = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
  font-size: 14px;
`;

export const SwapButton = styled.button`
  background: ${({ disabled }) => (disabled ? '#393a54' : '#3ad1c7')};
  color: ${({ disabled }) => (disabled ? '#bdbdbd' : '#23243a')};
  border: none;
  border-radius: 8px;
  padding: 14px 40px;
  font-size: 18px;
  font-weight: 700;
  cursor: ${({ disabled }) => (disabled ? 'not-allowed' : 'pointer')};
  margin-bottom: 8px;
  transition: background 0.2s;
`;

export const CreationsLeft = styled.div`
  color: #bdbdbd;
  font-size: 14px;
`;

export const Modal = styled.div`
  position: fixed;
  top: 0; left: 0; right: 0; bottom: 0;
  background: rgba(30,32,44,0.85);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
`;

export const ModalContent = styled.div`
  background: #23243a;
  border-radius: 16px;
  padding: 40px 32px;
  min-width: 340px;
  color: #fff;
  text-align: center;
`;

export const ModalButton = styled.button`
  background: #3ad1c7;
  color: #23243a;
  border: none;
  border-radius: 8px;
  padding: 12px 32px;
  font-size: 16px;
  font-weight: 700;
  margin-top: 24px;
  cursor: pointer;
`;

export const Loader = styled.div`
  font-size: 32px;
  color: #3ad1c7;
  margin-bottom: 16px;
`; 