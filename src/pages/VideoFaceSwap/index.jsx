import {
    CreationsLeft,
    FaceCircle,
    FaceSwapSection,
    FaceUploaders,
    GalleryItem,
    GalleryPanel,
    GalleryTitle,
    MainContent,
    Modal,
    ModalButton,
    ModalContent,
    PageContainer,
    Placeholder,
    SectionTitle,
    SwapButton,
    VideoBox,
    VideoPanel,
    VideoPlayer
} from './style.jsx'
import React, { useEffect, useRef, useState } from 'react'
import {
    useGetFaceSwapStatus,
    useStartFaceSwap,
    useUploadNewFace,
    useUploadVideoForFaceSwap
} from '../../Hooks/useVideoFaceSwap.js'

import Header from '../../components/Header'
import Player from '../Player/index.jsx'
import { TAB_LABELS } from '../../utils/constants'
import TabBar from '../../components/Tabs'
import { setActiveTab } from '../../redux/Reducers/uiReducer'
import { useDispatch } from 'react-redux'

const VideoFaceSwap = () => {
    const [selectedVideo, setSelectedVideo] = useState(null)
    const [uploadedFaces, setUploadedFaces] = useState([null, null])
    const [showNote, setShowNote] = useState(false)
    const [showGenerating, setShowGenerating] = useState(false)
    const [showResult, setShowResult] = useState(false)
    const [outputVideo, setOutputVideo] = useState(null)
    const [processingStatus, setProcessingStatus] = useState(null)
    const [credits, setCredits] = useState(null)
    const [generationId, setGenerationId] = useState(null)
    const [shouldGetVideoUrl, setShouldGetVideoUrl] = useState(false)
    const [wasVideoModalClosed, setWasVideoModalClosed] = useState(false)
    const fileInputRef = useRef()
    const dispatch = useDispatch()

    const {
        mutateAsync: uploadVideo,
        data: uploadVideoData,
    } = useUploadVideoForFaceSwap()

    const {
        mutateAsync: uploadFaceForSwap,
        data: uploadNewFaceData,
    } = useUploadNewFace()

    const {
        mutateAsync: startVideoFaceSwap,
    } = useStartFaceSwap()

    const {
        data: getFaceSwapStatusData,
        isSuccess: getFaceSwapSuccess,
    } = useGetFaceSwapStatus({ generationId, shouldGetVideoUrl })

    useEffect(() => {
        dispatch(setActiveTab(TAB_LABELS.VIDEO))
    }, [dispatch])

    useEffect(() => {
        if (wasVideoModalClosed) return

        if(getFaceSwapSuccess){
            if (getFaceSwapStatusData?.result_url) {
            setOutputVideo(getFaceSwapStatusData?.result_url)
            setShowResult(true)
            setShowGenerating(false)
            setProcessingStatus('Completed')
        } else {
            setShowGenerating(false)
            setProcessingStatus('No result found')
        }
        }
        
    }, [getFaceSwapSuccess, getFaceSwapStatusData])

    // Updated gallery data with 4 boxes
    const galleryItems = [
        { id: 1, value: 1 },
        { id: 2, value: 2 },
        { id: 3, value: 3 },
        { id: 4, value: 4 }
    ]

    // Handlers
    const handleGallerySelect = async (item) => {
        setSelectedVideo(item)
        setShowGenerating(true)

        try {
            await uploadVideo({ template_id: item.value, user_id: '1' })
            console.log('videoface swap data:', uploadVideoData)
            setProcessingStatus(uploadVideoData?.status)
            setCredits(uploadVideoData?.credits)
            setGenerationId(uploadVideoData?.generation_id)
            if (uploadVideoData?.detected_faces_urls) {
                const faceUrls = Object.values(
                    uploadVideoData?.detected_faces_urls
                )
                const newFaces = [null, null]
                if (faceUrls.length > 0) {
                    newFaces[0] = { url: faceUrls[0] }
                }
                setUploadedFaces(newFaces)
            }
        } catch (error) {
            console.error('Failed to process video:', error?.detail ?? error)
        } finally {
            setShowGenerating(false)
        }
    }

    const handleNoteAcknowledge = () => {
        setShowNote(false)
        fileInputRef.current.click()
    }

    // Handle click on the second face circle
    const handleSecondFaceClick = () => {
        if (generationId) {
            fileInputRef.current.click()
        } else {
            alert('Please select a video from the gallery first.')
        }
    }

    // Handle file upload for the second face
    const handleFileChange = async (e) => {
        const file = e.target.files[0]
        if (file && generationId) {
            // Show preview immediately
            const previewUrl = URL.createObjectURL(file)
            const newFaces = [...uploadedFaces]
            newFaces[1] = { url: previewUrl }
            setUploadedFaces(newFaces)

            try {
                setShowGenerating(true)
                await uploadFaceForSwap({
                    generation_id: generationId,
                    group_id: 0,
                    file
                })
                if (uploadNewFaceData?.target_url) {
                    newFaces[1] = { url: uploadNewFaceData?.target_url }
                    setUploadedFaces([...newFaces])
                }
            } catch (error) {
                console.error('Failed to upload new face:', error)
                // Optionally show an error or keep the preview
            } finally {
                setShowGenerating(false)
            }
        }
    }

    const handleSwap = async () => {
        if (!generationId) {
            alert('Cannot start swap without a generation ID.')
            return
        }

        setShowGenerating(true)
        setProcessingStatus('Swapping...')

        try {
            // 1. Start the swap and wait for the backend to finish
            await startVideoFaceSwap(generationId, {
                onSuccess: () => {
                    setShouldGetVideoUrl(true)
                }
            })
            console.log('successfully completed swap and fetching video url !')

            // while (!startFaceSwapSuccess || !getFaceSwapSuccess) {
            //     continue
            // }

            // console.log('successfully fetched results')
        } catch (error) {
            console.error('Failed to start face swap:', error)
            setShowGenerating(false)
            setProcessingStatus('Error')
        }
    }

    return (
        <div style={{ background: '#18192a', minHeight: '100vh' }}>
            <Header />
            <div
                style={{ maxWidth: 1400, margin: '0 auto', padding: '32px 0' }}
            >
                <TabBar />
                <PageContainer>
                    <MainContent>
                        <GalleryPanel>
                            <GalleryTitle>Choose From Gallery</GalleryTitle>
                            {galleryItems.map((item) => (
                                <GalleryItem
                                    key={item.id}
                                    selected={
                                        selectedVideo &&
                                        selectedVideo.id === item.id
                                    }
                                    onClick={() => handleGallerySelect(item)}
                                >
                                    {item.value}
                                </GalleryItem>
                            ))}
                        </GalleryPanel>
                        <VideoPanel>
                            <SectionTitle>Source Video</SectionTitle>
                            <VideoBox>
                                {selectedVideo ? (
                                    <VideoPlayer
                                        src={selectedVideo.src}
                                        controls
                                        poster={selectedVideo.thumbnail}
                                    />
                                ) : (
                                    <Placeholder>Select a video</Placeholder>
                                )}
                            </VideoBox>
                            <FaceSwapSection>
                                <FaceUploaders>
                                    {[0, 1].map((idx) => (
                                        <FaceCircle
                                            key={idx}
                                            onClick={
                                                idx === 1
                                                    ? handleSecondFaceClick
                                                    : undefined
                                            }
                                        >
                                            {uploadedFaces[idx]?.url ? (
                                                <img
                                                    src={uploadedFaces[idx].url}
                                                    alt={`Face ${idx + 1}`}
                                                />
                                            ) : (
                                                '+'
                                            )}
                                        </FaceCircle>
                                    ))}
                                </FaceUploaders>
                                <SwapButton
                                    onClick={handleSwap}
                                    disabled={
                                        !uploadedFaces[0] ||
                                        !uploadedFaces[1] ||
                                        showGenerating
                                    }
                                >
                                    Swap Face Now
                                </SwapButton>
                                {credits !== null && (
                                    <CreationsLeft>
                                        {credits} Creations Left
                                    </CreationsLeft>
                                )}
                                {processingStatus && (
                                    <div
                                        style={{
                                            textAlign: 'center',
                                            margin: '10px 0',
                                            color: '#fff'
                                        }}
                                    >
                                        Status: {processingStatus}
                                    </div>
                                )}
                            </FaceSwapSection>
                        </VideoPanel>
                    </MainContent>
                    {showNote && (
                        <Modal>
                            <ModalContent>
                                <h3>Note</h3>
                                <ol>
                                    <li>
                                        The user must upload a genuine image.
                                    </li>
                                    <li>
                                        Uploading unauthorised images is
                                        strictly prohibited.
                                    </li>
                                    <li>
                                        The uploaded image must display the full
                                        face to ensure it can be swapped
                                        correctly.
                                    </li>
                                    <li>Image Size should be within 5 MB</li>
                                    <li>Image Formats should be JPG, PNG</li>
                                </ol>
                                <ModalButton onClick={handleNoteAcknowledge}>
                                    I Understand
                                </ModalButton>
                            </ModalContent>
                        </Modal>
                    )}
                    {showGenerating && (
                        <Modal>
                            <ModalContent>
                                <p>Processing Video...</p>
                            </ModalContent>
                        </Modal>
                    )}
                    {showResult && (
                        <Modal>
                            <ModalContent>
                                <h3>Output Video</h3>
                                <div style={{ width: '100%' }}>
                                    <Player videoUrl={outputVideo} />
                                </div>
                                <ModalButton
                                    onClick={() => {
                                        setShowResult(false)
                                        setWasVideoModalClosed(true)
                                    }}
                                >
                                    Close
                                </ModalButton>
                            </ModalContent>
                        </Modal>
                    )}
                    <input
                        type="file"
                        ref={fileInputRef}
                        style={{ display: 'none' }}
                        accept="image/jpeg,image/png"
                        onChange={handleFileChange}
                    />
                </PageContainer>
            </div>
        </div>
    )
}

export default VideoFaceSwap
