import React, { useEffect, useState, useRef } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import {
    getTemplates,
    clearSwapResult,
    clearAll
} from '../../redux/Reducers/faceSwapReducer'
import { setActiveTab } from '../../redux/Reducers/uiReducer'
import {
    PageContainer,
    GalleryContainer,
    GalleryTitle,
    GalleryList,
    GalleryImage,
    MainContent,
    SectionTitle,
    FlexRow,
    SourceImage,
    UploadCircle,
    SwapButton,
    TermsRow,
    OutputImage,
    CenteredModal,
    ModalContent,
    ModalButton
} from '../SingleFaceSwap/style'
import { Header, TabBar } from '../../components'
import { NOTE_TEXT, TAB_LABELS } from '../../utils/constants'
import {
    useFaceSwap,
    useGetAllFSTemplates,
    useGetFSTemplateInfo,
    useUploadFSTarget,
    useUploadFSTemplate
} from '../../Hooks/useFaceSwap'

const DoubleFaceSwap = () => {
    const dispatch = useDispatch()
    const {
        templatesStatus,
        uploadStatus,
        uploadError,
    } = useSelector((state) => state.faceSwap)
    const user = useSelector((state) => state.auth.user)

    const [selectedTemplate, setSelectedTemplate] = useState(null)
    const [uploadedFaces, setUploadedFaces] = useState([null, null])
    const [uploadedFaceUrls, setUploadedFaceUrls] = useState([null, null])
    const [showNote, setShowNote] = useState(false)
    const [showUploadSuccess, setShowUploadSuccess] = useState(false)
    const [uploadingFaceIndex, setUploadingFaceIndex] = useState(null)
    const [agreed, setAgreed] = useState(false)
    const [showResult, setShowResult] = useState(false)
    const [showLoading, setShowLoading] = useState(false)
    const [outputImage, setOutputImage] = useState(null)
    const [showImage, setShowImage] = useState(false)

    // react query implementation
    const {
        data: templatesData,
    } = useGetAllFSTemplates()

    const {
        isLoading: templateInfoLoading,
    } = useGetFSTemplateInfo({ template_id: selectedTemplate?.template_id})

    const { mutate: uploadTemplateMutate, 
        data: templateUploadData,
        isSuccess: templateUploadSuccess
    } = useUploadFSTemplate()

    const { mutateAsync: uploadTargetMutate,
        data: targetUploadData,
    } = useUploadFSTarget()

    const { mutateAsync: faceSwapMutate,
        isLoading: faceSwapLoading,
        isError: faceSwapError,
        data: faceSwapData,
        isSuccess: faceSwapSuccess
    } = useFaceSwap()

    useEffect(() => {
            console.log('templatesData: ', templatesData)
        }, [templatesData])

    const handleResetState = () => {
        setUploadedFaces([null, null]);
        setUploadedFaceUrls([null, null]);
        setOutputImage(null);
        setShowResult(false);
        setAgreed(false);
    }

    // show image after 2 sec
        useEffect(() => {
            if (selectedTemplate && templateUploadSuccess) {
                const timer = setTimeout(() => {
                    setShowImage(true)
                }, 1000)
    
                return () => clearTimeout(timer)
            } else {
                setShowImage(false)
            }
        }, [templateUploadSuccess])



    const fileInputRef = useRef()

    useEffect(() => {
        dispatch(clearAll())
        dispatch(getTemplates())
    }, [dispatch])

    useEffect(() => {
        if (faceSwapSuccess && faceSwapData) {
            setOutputImage(
                faceSwapData.signed_swap_url ||
                    faceSwapData.output_url ||
                    faceSwapData.result_url ||
                    faceSwapData.image_url
            )
            setShowResult(true)
            setShowLoading(false)
        } else if (faceSwapLoading) {
            setShowLoading(true)
        } else if (faceSwapError) {
            setShowLoading(false)
            alert('Face swap failed: ' + (faceSwapError || 'Please try again.'))
        }
    }, [faceSwapSuccess, faceSwapData, faceSwapError])

    useEffect(() => {
        if (uploadStatus === 'failed') {
            alert('Face upload failed: ' + (uploadError || 'Please try again.'))
        }
    }, [uploadStatus, uploadError, dispatch])

    // useEffect(() => {
    //     if (templateInfoStatus === 'succeeded' && templateInfo) {
    //         console.log('Template Info:', templateInfo)
    //         const formData = new FormData()
    //         formData.append('template_id', templateInfo.template_id)
    //         if (user && user.id) {
    //             formData.append('user_id', user.id)
    //         }
    //         dispatch(uploadTemplate(formData))
    //         dispatch(clearTemplateInfo())
    //     } else if (templateInfoStatus === 'failed') {
    //         alert('Failed to get template info.')
    //         dispatch(clearTemplateInfo())
    //     }
    // }, [templateInfoStatus, templateInfo, dispatch, user])

    // useEffect(() => {
    //     if (templateUploadStatus === 'succeeded' && templateUploadResult) {
    //         console.log('Template Upload Result:', templateUploadResult)
    //         if (templateUploadResult.signed_detected_face_urls && templateUploadResult.signed_detected_face_urls.length > 0) {
    //             const urls = [...detectedFaceUrls]
    //             urls[0] = templateUploadResult.signed_detected_face_urls[0] || null
    //             if (templateUploadResult.signed_detected_face_urls.length > 1) {
    //                 urls[1] = templateUploadResult.signed_detected_face_urls[1] || null
    //             }
    //             setDetectedFaceUrls(urls)
    //         }
    //     } else if (templateUploadStatus === 'failed') {
    //         alert('Failed to upload template.')
    //         dispatch(clearTemplateUploadResult())
    //     }
    // }, [templateUploadStatus, templateUploadResult, dispatch])

    useEffect(() => {
        if (uploadedFaces[0] && uploadedFaces[1]) {
            if (!templateUploadData || !templateUploadData.generation_id) {
                alert(
                    'Something went wrong after template selection. Please try again.'
                )
                return
            }

            const formData = new FormData()
            formData.append('generation_id', templateUploadData.generation_id)
            formData.append('files', uploadedFaces[0])
            formData.append('files', uploadedFaces[1])
            if (user && user.id) {
                formData.append('user_id', user.id)
            }
            uploadTargetMutate(formData)
            setShowUploadSuccess(true)
        }
    }, [uploadedFaces, templateUploadData, user])

    const handleGallerySelect = (template) => {
        handleResetState()
        setSelectedTemplate(template)
        console.log('Selected Template:', template)
        const formData = new FormData()
        formData.append('template_id', template.template_id)
        if (user && user.id) {
            formData.append('user_id', user.id)
        }
        uploadTemplateMutate(formData)

    }

    const handleUploadClick = (idx) => {
        if (!selectedTemplate) {
            alert('Please select a template from the gallery first.')
            return
        }
        setUploadingFaceIndex(idx)
        setShowNote(true)
    }

    const handleNoteAcknowledge = () => {
        setShowNote(false)
        fileInputRef.current.click()
    }

    const handleFileChange = async (e) => {
        const file = e.target.files[0]
        if (!file) return

        handleCloseResult()

        if (!templateUploadData || !templateUploadData.generation_id) {
            alert(
                'Something went wrong after template selection. Please try again.'
            )
            return
        }

        if (
            !['image/jpeg', 'image/png'].includes(file.type) ||
            file.size > 5 * 1024 * 1024
        ) {
            alert('Invalid file. Please upload a JPG or PNG under 5MB.')
            return
        }

        const newFaces = [...uploadedFaces]
        const newUrls = [...uploadedFaceUrls]
        newFaces[uploadingFaceIndex] = file
        newUrls[uploadingFaceIndex] = URL.createObjectURL(file)
        setUploadedFaces(newFaces)
        setUploadedFaceUrls(newUrls)

    }

    const handleUploadSuccessProceed = () => {
        setShowUploadSuccess(false)
    }

    const handleSwap = async () => {
        if (!targetUploadData || !targetUploadData.generation_id) {
            alert('Please upload both target faces first.')
            return
        }

        // Ensure both faces have been uploaded by checking for a valid URL in the uploadResult
        // This part of logic depends on what `uploadResult` looks like after two uploads.
        // Assuming the final uploadResult after the second upload contains all necessary info.
        if (uploadedFaces.filter((f) => f !== null).length < 2) {
            alert('Please upload both target faces.')
            return
        }

        setShowLoading(true)

        const swapData = {
            generation_id: targetUploadData.generation_id,
            source_indices: [0, 1],
            target_indices: [0, 1]
        }

        faceSwapMutate(swapData)
    }

    const handleCloseResult = () => {
        setShowResult(false)
        setOutputImage(null)
        dispatch(clearSwapResult())
    }

    // Set active tab when component mounts
    useEffect(() => {
        dispatch(setActiveTab(TAB_LABELS.MULTIPLE))
    }, [dispatch])

    return (
        <div style={{ background: '#18192a', minHeight: '100vh' }}>
            <Header credits={4} />
            <div
                style={{ maxWidth: 1400, margin: '0 auto', padding: '32px 0' }}
            >
                {/* Replace the tabs section with the TabBar component */}
                <TabBar />

                <PageContainer
                    style={{ boxShadow: '0 4px 32px #0002', borderRadius: 24 }}
                >
                    {/* Gallery */}
                    <GalleryContainer
                        style={{ padding: 18, minWidth: 220, maxHeight: 540 }}
                    >
                        <GalleryTitle
                            style={{ fontSize: 18, marginBottom: 18 }}
                        >
                            Choose From Gallery
                        </GalleryTitle>
                        <GalleryList>
                            {templatesStatus === 'loading' && (
                                <div>Loading...</div>
                            )}
                            {templatesStatus === 'failed' && (
                                <div>Failed to load gallery</div>
                            )}
                            {Array.isArray(templatesData.available_templates) &&
                                templatesData.available_templates
                                    .map((template, idx) => (
                                    <GalleryImage
                                        key={template.template_id || idx}
                                        src={template.signed_template_url}
                                        alt={template.filename || 'template'}
                                        selected={
                                            selectedTemplate &&
                                            selectedTemplate.template_id ===
                                                template.template_id
                                        }
                                        onClick={() =>
                                            handleGallerySelect(template)
                                        }
                                        style={{
                                            border:
                                                selectedTemplate &&
                                                selectedTemplate.template_id ===
                                                    template.template_id
                                                    ? '3px solid #3ad1c7'
                                                    : '2px solid #23243a',
                                            boxShadow:
                                                selectedTemplate &&
                                                selectedTemplate.template_id ===
                                                    template.template_id
                                                    ? '0 0 0 2px #2ec6ff'
                                                    : 'none',
                                            cursor: 'pointer',
                                            background: '#191a2a'
                                        }}
                                    />
                                ))}
                        </GalleryList>
                    </GalleryContainer>
                    {/* Main Content */}
                    <MainContent
                        style={{
                            background: '#23243a',
                            display: 'flex',
                            flexDirection: 'column',
                            justifyContent: 'space-between'
                        }}
                    >
                        <SectionTitle
                            style={{
                                fontWeight: 700,
                                fontSize: 26,
                                marginBottom: 18,
                                textAlign: 'center'
                            }}
                        >
                            ErosNow Photos & Video Face Swap AI
                        </SectionTitle>
                        <FlexRow
                            style={{
                                justifyContent: 'center',
                                alignItems: 'flex-start',
                                gap: 48
                            }}
                        >
                            {/* Source Image */}
                            <div
                                style={{
                                    flex: 1,
                                    display: 'flex',
                                    flexDirection: 'column',
                                    alignItems: 'center'
                                }}
                            >
                                <div
                                    style={{
                                        fontWeight: 600,
                                        fontSize: 18,
                                        marginBottom: 12
                                    }}
                                >
                                    Source Image
                                </div>
                                {selectedTemplate ? (
                                    <div
                                        style={{
                                            position: 'relative',
                                            width: 280,
                                            height: 320,
                                            borderRadius: 16,
                                            border: '0px solid #3ad1c7',
                                            boxShadow: '0 0 0 2px #2ec6ff'
                                        }}
                                    >
                                        <SourceImage
                                            src={selectedTemplate.signed_template_url}
                                            alt="Source"
                                            style={{
                                                width: '100%',
                                                height: '100%'
                                            }}
                                        />
                                    </div>
                                ) : (
                                    <div
                                        style={{
                                            width: 280,
                                            height: 320,
                                            background: '#393a54',
                                            borderRadius: 16,
                                            display: 'flex',
                                            alignItems: 'center',
                                            justifyContent: 'center',
                                            color: '#bdbdbd',
                                            border: '3px solid transparent'
                                        }}
                                    >
                                        Select a source image
                                    </div>
                                )}
                            </div>
                            {/* Arrow and Face Upload */}
                            <div
                                style={{
                                    flex: 1,
                                    display: 'flex',
                                    flexDirection: 'column',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    minWidth: 320
                                }}
                            >
                                <div
                                    style={{
                                        fontWeight: 600,
                                        fontSize: 18,
                                        marginBottom: 12
                                    }}
                                >
                                    Add your Face to Swap
                                </div>
                                <div
                                    style={{
                                        display: 'flex',
                                        flexDirection: 'column',
                                        gap: 32,
                                        marginBottom: 18
                                    }}
                                >
                                    {[0, 1].map((idx) => (
                                        <div
                                            key={idx}
                                            style={{
                                                display: 'flex',
                                                alignItems: 'center',
                                                gap: 32
                                            }}
                                        >
                                            {/* Current face (circle) */}
                                            <div>
                                                {selectedTemplate ? (
                                                    showImage ? (
                                                        <img
                                                            src={
                                                                templateUploadData
                                                                ?.signed_detected_face_urls[
                                                                    idx
                                                                ]
                                                            }
                                                            alt={`Detected Face ${idx + 1}`}
                                                            style={{
                                                                width: 80,
                                                                height: 80,
                                                                borderRadius:
                                                                    '50%',
                                                                objectFit:
                                                                    'cover',
                                                                border: '2px solid #3ad1c7',
                                                                background:
                                                                    '#191a2a'
                                                            }}
                                                        />
                                                    ) : (
                                                        <UploadCircle
                                                            style={{
                                                                background:
                                                                    '#393a54',
                                                                color: '#bdbdbd',
                                                                border: '2px dashed #3ad1c7'
                                                            }}
                                                        >
                                                            ?
                                                        </UploadCircle>
                                                    )
                                                ) : (
                                                    <UploadCircle
                                                        style={{
                                                            border: '2px dashed #3ad1c7'
                                                        }}
                                                    ></UploadCircle>
                                                )}
                                            </div>
                                            <span
                                                style={{
                                                    fontSize: 38,
                                                    color: '#bdbdbd',
                                                    fontWeight: 700
                                                }}
                                            >
                                                →
                                            </span>
                                            {/* Upload face (circle) */}
                                            <div>
                                                {uploadedFaceUrls[idx] ? (
                                                    <img
                                                        src={
                                                            uploadedFaceUrls[
                                                                idx
                                                            ]
                                                        }
                                                        alt={`Uploaded Face ${idx + 1}`}
                                                        style={{
                                                            width: 80,
                                                            height: 80,
                                                            borderRadius: '50%',
                                                            objectFit: 'cover',
                                                            border: '2px solid #3ad1c7',
                                                            background:
                                                                '#191a2a',
                                                            boxSizing:
                                                                'border-box'
                                                        }}
                                                    />
                                                ) : (
                                                    <UploadCircle
                                                        onClick={() =>
                                                            handleUploadClick(
                                                                idx
                                                            )
                                                        }
                                                        style={{
                                                            border: '2px dashed #3ad1c7'
                                                        }}
                                                    >
                                                        +
                                                    </UploadCircle>
                                                )}
                                            </div>
                                        </div>
                                    ))}
                                </div>
                                <input
                                    type="file"
                                    ref={fileInputRef}
                                    style={{ display: 'none' }}
                                    onChange={handleFileChange}
                                    accept="image/png, image/jpeg"
                                />
                                <div
                                    style={{
                                        display: 'flex',
                                        flexDirection: 'column',
                                        alignItems: 'center',
                                        marginTop: 18
                                    }}
                                >
                                    <TermsRow
                                        style={{
                                            justifyContent: 'center',
                                            marginTop: 18
                                        }}
                                    >
                                        <input
                                            type="checkbox"
                                            checked={agreed}
                                            onChange={(e) =>
                                                setAgreed(e.target.checked)
                                            }
                                            id="terms-agree"
                                            style={{
                                                marginRight: 8,
                                                accentColor: '#3ad1c7'
                                            }}
                                        />
                                        <span
                                            style={{
                                                color: '#bdbdbd',
                                                fontSize: 15
                                            }}
                                        >
                                            I agree to the{' '}
                                            <a
                                                href="#"
                                                style={{ color: '#3ad1c7' }}
                                            >
                                                Terms of use
                                            </a>{' '}
                                            &{' '}
                                            <a
                                                href="#"
                                                style={{ color: '#3ad1c7' }}
                                            >
                                                Privacy Policy
                                            </a>
                                        </span>
                                    </TermsRow>
                                    <SwapButton
                                        onClick={handleSwap}
                                        disabled={!agreed || showLoading || templateInfoLoading}
                                    >
                                        {showLoading
                                            ? 'Swapping...'
                                            : 'Face Swap'}
                                    </SwapButton>
                                    <div
                                        style={{
                                            marginTop: 8,
                                            color: '#bdbdbd',
                                            fontSize: 15
                                        }}
                                    >
                                        4 Creations Left
                                    </div>
                                </div>
                            </div>
                        </FlexRow>
                        {/* Result Modal */}
                        {showResult && outputImage && (
                            <CenteredModal>
                                <ModalContent>
                                    <SectionTitle>
                                        Face Swap Result
                                    </SectionTitle>
                                    <OutputImage
                                        src={outputImage}
                                        alt="Face Swap Result"
                                    />
                                    <div style={{ display: 'flex', gap: 16 }}>
                                        <ModalButton
                                            onClick={handleCloseResult}
                                        >
                                            Close
                                        </ModalButton>
                                        <ModalButton>Download</ModalButton>
                                    </div>
                                </ModalContent>
                            </CenteredModal>
                        )}
                        {/* Note Modal */}
                        {showNote && (
                            <CenteredModal>
                                <ModalContent>
                                    <SectionTitle>Please Note</SectionTitle>
                                    <ul
                                        style={{
                                            textAlign: 'left',
                                            color: '#bdbdbd',
                                            fontSize: 16,
                                            marginBottom: 24,
                                            paddingLeft: 20
                                        }}
                                    >
                                        {NOTE_TEXT.map((note, i) => (
                                            <li key={i}>{note}</li>
                                        ))}
                                    </ul>
                                    <ModalButton
                                        onClick={handleNoteAcknowledge}
                                    >
                                        Proceed to Upload
                                    </ModalButton>
                                </ModalContent>
                            </CenteredModal>
                        )}
                        {/* Upload Success Modal */}
                        {showUploadSuccess && (
                            <CenteredModal>
                                <ModalContent>
                                    <SectionTitle>
                                        Upload Successful!
                                    </SectionTitle>
                                    <p
                                        style={{
                                            color: '#bdbdbd',
                                            fontSize: 16,
                                            marginBottom: 24
                                        }}
                                    >
                                        Your image has been uploaded
                                        successfully.
                                    </p>
                                    <ModalButton
                                        onClick={handleUploadSuccessProceed}
                                    >
                                        OK
                                    </ModalButton>
                                </ModalContent>
                            </CenteredModal>
                        )}
                    </MainContent>
                </PageContainer>
            </div>
        </div>
    )
}

export default DoubleFaceSwap
