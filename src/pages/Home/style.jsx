import styled from 'styled-components'

export const HeroSection = styled.section`
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 48px 5vw 32px 5vw;
    background: linear-gradient(120deg, #23283a 60%, #e6e6e6 100%);
    min-height: 420px;
    position: relative;
`

export const HeroText = styled.div`
    max-width: 480px;
    color: #fff;
`

export const Intro = styled.div`
    font-size: 1.1rem;
    color: #b2f0e6;
    font-style: italic;
    margin-bottom: 8px;
`

export const Title = styled.h1`
    font-size: 2.6rem;
    font-weight: 700;
    margin: 0 0 8px 0;
    color: #3ad0a1;
    letter-spacing: 1px;
`

export const Subtitle = styled.div`
    font-size: 1.2rem;
    color: #b2f0e6;
    margin-bottom: 24px;
`

export const FaceSwapTag = styled.div`
    position: relative;
    left: 162px;
    width: 100px;
    background: #ffb36b;
    color: #fff;
    border-radius: 8px;
    padding: 4px 14px;
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 12px;
`

export const MagicText = styled.h2`
    font-size: 2rem;
    color: #fff;
    margin: 0 0 18px 0;
    font-weight: 600;
`

export const TryOutButton = styled.button`
    background: #23283a;
    color: #fff;
    border: none;
    border-radius: 24px;
    padding: 10px 28px 10px 18px;
    font-size: 1.1rem;
    font-weight: 500;
    margin-top: 18px;
    display: flex;
    align-items: center;
    gap: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    cursor: pointer;
    position: relative;
    left: 45px;
`

export const HeroImageWrapper = styled.div`
    position: relative;
    width: 340px;
    height: 340px;
    display: flex;
    align-items: center;
    justify-content: center;
`

export const HeroImage = styled.img`
    width: 100%;
    height: 100%;
    border-radius: 18px;
    object-fit: cover;
`

export const BeforeAfterTag = styled.div`
    position: absolute;
    bottom: 18px;
    left: 24px;
    background: #ffb36b;
    color: #fff;
    border-radius: 8px;
    padding: 2px 12px;
    font-size: 0.95rem;
    font-weight: 600;
`
export const AfterTag = styled(BeforeAfterTag)`
    left: auto;
    right: 54px;
`

export const FeaturesSection = styled.section`
    background: linear-gradient(120deg, #23283a 60%, #e6e6e6 100%);
    padding: 32px 5vw 64px 5vw;
`

export const FeaturesTitle = styled.h2`
    color: #fff;
    font-size: 2rem;
    font-weight: 600;
    margin-bottom: 32px;
    text-align: center;
`

export const FeaturesRow = styled.div`
    display: flex;
    gap: 32px;
    justify-content: center;
    flex-wrap: wrap;
`

export const FeatureCard = styled.div`
    background: #23283a;
    border-radius: 18px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    width: 300px;
    padding: 0 0 24px 0;
    display: flex;
    flex-direction: column;
    align-items: center;
`

export const FeatureImage = styled.img`
    width: 100%;
    height: 50vh;
    object-fit: cover;
    border-radius: 18px 18px 0 0;
`

export const FeatureTitle = styled.h3`
    color: #fff;
    font-size: 1.2rem;
    font-weight: 600;
    margin: 18px 0 8px 0;
`

export const FeatureDesc = styled.p`
    color: #ccc;
    font-size: 1rem;
    margin: 0 0 18px 0;
    text-align: center;
`

export const LearnMoreBtn = styled.button`
    background: #10131a;
    color: #fff;
    border: none;
    border-radius: 8px;
    padding: 8px 24px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
`

export const Tag = styled.div`
    display: inline-block;
    background: #ffb36b;
    color: #fff;
    border-radius: 8px;
    padding: 4px 14px;
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 12px;
`

export const HomeTopDiv = styled.div`
    display: flex;
    margin-top: 24px;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background: linear-gradient(
        rgba(0, 184, 248, 0),
        rgba(0, 184, 248, 0.4),
        rgba(0, 184, 248, 0)
    );
`
