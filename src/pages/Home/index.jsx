import {
    AfterTag,
    BeforeAfterTag,
    FaceSwapTag,
    FeatureCard,
    FeatureDesc,
    FeatureImage,
    FeatureTitle,
    FeaturesRow,
    FeaturesSection,
    FeaturesTitle,
    HeroImage,
    HeroImageWrapper,
    HeroSection,
    HeroText,
    HomeTopDiv,
    LearnMoreBtn,
    MagicText,
    Tag,
    TryOutButton
} from './style'
import { <PERSON>ton, Header, HomePageRails, Text, MobileTabBar } from '../../components'
import React, { useEffect } from 'react'
import {
    erosuniverselogo,
    mainImage,
    multiFaceSwap,
    singleFaceSwap,
    videoFaceSwap
} from '../../assets'


import { homeData } from '../../redux/Reducers/homeReducer'
import { useDispatch } from 'react-redux'
import { useNavigate } from 'react-router-dom'

const Home = () => {
    const navigate = useNavigate()
    const dispatch = useDispatch()
    useEffect(() => {
        dispatch(homeData())
    }, [])

    return (
        <div>
            {/* <HeroSection>
                <HeroText>
                    <img
                        src={erosuniverselogo}
                        alt="Eros Universe Logo"
                        style={{ width: '260px', marginBottom: '18px' }}
                    />
                    <FaceSwapTag>Face Swap</FaceSwapTag>
                    <MagicText>
                        Where Imagination Meets Reality—Face Swap Magic!
                    </MagicText>
                    <TryOutButton>
                        Try Out <span style={{ fontSize: '1.3em' }}>➔</span>
                    </TryOutButton>
                </HeroText>
                <HeroImageWrapper>
                    <HeroImage src={mainImage} alt="Face Swap Example" />
                    <BeforeAfterTag>Before</BeforeAfterTag>
                    <AfterTag>After</AfterTag>
                </HeroImageWrapper>
            </HeroSection>
            <FeaturesSection>
                <Tag style={{ marginBottom: 18 }}>Powerful Features</Tag>
                <FeaturesTitle>
                    AI-Powered web platform for fast & easy content creation
                </FeaturesTitle>
                <FeaturesRow>
                    <FeatureCard>
                        <FeatureImage
                            src={singleFaceSwap}
                            alt="Single Face Swap"
                        />
                        <FeatureTitle>Single Face Swap</FeatureTitle>
                        <FeatureDesc>
                            Change out faces as part of an animation or moving
                            photos.
                        </FeatureDesc>
                        <LearnMoreBtn
                            onClick={() => navigate('/single-face-swap')}
                        >
                            Learn More
                        </LearnMoreBtn>
                    </FeatureCard>
                    <FeatureCard>
                        <FeatureImage
                            src={multiFaceSwap}
                            alt="Multiple Face Swap"
                        />
                        <FeatureTitle>Multiple Face Swap</FeatureTitle>
                        <FeatureDesc>
                            Change multiple faces in a single image for group
                            photos.
                        </FeatureDesc>
                        <LearnMoreBtn
                            onClick={() => navigate('/double-face-swap')}
                        >
                            Learn More
                        </LearnMoreBtn>
                    </FeatureCard>
                    <FeatureCard>
                        <FeatureImage
                            src={videoFaceSwap}
                            alt="Video Face Swap"
                        />
                        <FeatureTitle>Video Face Swap</FeatureTitle>
                        <FeatureDesc>
                            Edit high quality videos with changing features.
                        </FeatureDesc>
                        <LearnMoreBtn
                            onClick={() => navigate('/video-face-swap')}
                        >
                            Learn More
                        </LearnMoreBtn>
                    </FeatureCard>
                </FeaturesRow>
            </FeaturesSection> */}
            <Header credits={10} />
            <HomeTopDiv>
                <Text
                    text={'Create, remix, and explore with the Power of AI'}
                    color={'white'}
                    fontSize={'20px'}
                    width={'296px'}
                    gradientText
                    setMargin
                ></Text>
                <Button
                    size={'14px'}
                    mt={'16px'}
                    mb={'16px'}
                    onClick={() => navigate('/create')}
                >
                    How It Works
                </Button>
                <Text
                    text={'Get started with'}
                    color={'white'}
                    fontSize={'14px'}
                ></Text>
            </HomeTopDiv>
            <HomePageRails></HomePageRails>
            <MobileTabBar />
        </div>
    )
}

export default Home
