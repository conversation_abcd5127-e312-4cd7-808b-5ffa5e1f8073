var jsonData = {
    TVjson: [
        {
            control: 'upnexttv',
            active: true,
            styleCSS: {
                style: [
                    {
                        bottom: '19%;'
                    },
                    {
                        position: 'absolute;'
                    },
                    {
                        right: '15.5%;'
                    },
                    {
                        height: '7%;'
                    },
                    {
                        'border-radius': '10px;'
                    },
                    {
                        outline: 'none;'
                    },
                    {
                        width: '15%;'
                    },
                    {
                        display: 'none;'
                    },

                    {
                        transform: 'rotate(0deg);'
                    },
                    {
                        color: 'rgba(255, 255, 255, 0.8);'
                    },
                    {
                        cursor: 'pointer;'
                    },
                    {
                        'background-repeat': 'no-repeat;'
                    },
                    {
                        ' background-position': 'center;'
                    },
                    {
                        border: '2px solid white;'
                    }
                ]
            },
            text: '',
            icon: 'imagpath',
            hovertext: ''
        },
        {
            control: 'liveButtonv18',
            active: true,
            styleCSS: {
                style: [
                    {
                        position: 'absolute;'
                    },
                    // {
                    // "outline":"none;"
                    // },
                    {
                        right: '3.2%;'
                    },
                    {
                        height: '3.4%;'
                    },
                    {
                        width: '4.5%;'
                    },
                    {
                        top: '87%;'
                    },
                    {
                        display: 'none;'
                    },
                    // {
                    // "font-family": "Rubik;"
                    // },

                    // {
                    // "z-index":"1;"
                    // },
                    // {
                    // "color": "#ffffff;"
                    // },
                    {
                        'font-size': '16px !important;'
                    },

                    {
                        'font-weight': '500;'
                    },

                    {
                        'background-color': 'red;'
                    }

                    // {
                    // "font-stretch": "normal;"
                    // },
                    // {
                    // "font-style": "normal;"

                    // },
                    // {
                    // "letter-spacing": "normal;"
                    // }
                ]
            },
            text: '',
            icon: 'imagpath',
            hovertext: ''
        },

        {
            control: 'goliveButtonv18',
            active: true,
            styleCSS: {
                style: [
                    {
                        position: 'absolute;'
                    },
                    {
                        right: '1.93%;'
                    },
                    {
                        height: '40px;'
                    },
                    {
                        width: '106px;'
                    },
                    {
                        bottom: '3.43%;'
                    },
                    {
                        'background-color': 'rgba(0,0,0, 0.5);'
                    },
                    {
                        color: 'rgba(255, 255, 255, 0.8) !important;'
                    },
                    {
                        'border-radius': '16px;'
                    },
                    {
                        'font-size': '16px;'
                    },

                    {
                        'font-family': 'Rubik;'
                    }

                    // {
                    // "z-index":"1;"
                    // },

                    // // {
                    // // "cursor":"pointer;"
                    // // },

                    // {
                    // "color": "#ffffff;"
                    // },
                    // {
                    // "font-size": "1.1713vw;"
                    // },
                    // // {
                    // // "border-radius": "6px;"
                    // // },
                    // {
                    // "font-stretch": "normal;"
                    // },
                    // {
                    // "font-style": "normal;"

                    // },
                    // {
                    // "letter-spacing": "normal;"
                    // }
                ]
            },
            text: 'Go LIVE',
            icon: 'imagpath',
            hovertext: ''
        },

        {
            control: 'recommended_tv',
            active: true,
            styleCSS: {
                style: [
                    {
                        position: 'absolute;'
                    },

                    {
                        height: '100% !important;'
                    },

                    {
                        width: '100%;'
                    },

                    {
                        'z-index': '1;'
                    },
                    {
                        display: 'none;'
                    }
                ]
            },
            text: '',
            icon: 'imagpath',
            hovertext: ''
        },
        {
            control: 'live_buttonTv',
            active: false,
            styleCSS: {
                style: [
                    {
                        position: 'absolute;'
                    },
                    {
                        outline: 'none;'
                    },
                    {
                        left: '83.6%;'
                    },
                    {
                        height: '4.4%;'
                    },
                    {
                        width: '6.6%;'
                    },
                    {
                        display: 'none;'
                    },
                    {
                        'z-index': '1;'
                    },
                    {
                        top: '9.5%;'
                    },
                    {
                        cursor: 'pointer;'
                    },
                    {
                        'background-color': '#5cb617;'
                    },
                    {
                        color: '#ffffff;'
                    },
                    {
                        'font-size': '36px;'
                    },
                    {
                        'border-radius': '6px;'
                    },
                    {
                        'font-stretch': 'normal;'
                    },
                    {
                        'font-style': 'normal;'
                    },
                    {
                        'letter-spacing': 'normal;'
                    }
                ]
            },
            text: 'LIVE',
            icon: 'imagpath',
            hovertext: ''
        },

        {
            control: 'nextEpisode_button',
            active: false,
            styleCSS: {
                style: [
                    {
                        position: 'absolute;'
                    },
                    {
                        display: 'none;'
                    },
                    {
                        outline: 'none;'
                    },
                    {
                        'font-weight': 'bold;'
                    },
                    {
                        left: '5.2%;'
                    },
                    {
                        height: '4.3%;'
                    },
                    {
                        'border-radius': '5px;'
                    },
                    {
                        width: '13%;'
                    },

                    {
                        'background-repeat': 'no-repeat;'
                    },
                    {
                        display: 'block;'
                    },
                    {
                        'z-index': '1;'
                    },
                    {
                        top: '72.6%;'
                    },
                    // {
                    //    "bottom":"33.1%;"
                    // },
                    {
                        cursor: 'pointer;'
                    },
                    {
                        'padding-left': '1%;'
                    },

                    {
                        'background-image':
                            'url(player/assets/player_assets/ic-backbuttontv/ic_next.png);'
                    },
                    {
                        color: '#ffffff;'
                    },
                    {
                        'font-size': '24px;'
                    },
                    // {
                    //    "font-family": "ProximaNova-Semibold;"
                    // },
                    {
                        'font-stretch': 'normal;'
                    },
                    {
                        'font-style': 'normal;'
                    },
                    {
                        'letter-spacing': 'normal;'
                    },
                    {
                        'background-size': '48px;'
                    }
                ]
            },
            text: 'Next Episode',
            icon: 'imagpath',
            hovertext: ''
        },
        {
            control: 'setting_button',
            active: false,
            styleCSS: {
                style: [
                    {
                        bottom: '19%;'
                    },
                    {
                        position: 'absolute;'
                    },
                    {
                        left: '2.5%;'
                    },
                    {
                        height: '7%;'
                    },
                    {
                        'border-radius': '10px;'
                    },
                    {
                        outline: 'none;'
                    },
                    {
                        width: '16.82%;'
                    },
                    {
                        display: 'none;'
                    },

                    {
                        transform: 'rotate(0deg);'
                    },
                    {
                        color: 'rgba(255, 255, 255, 0.8);'
                    },
                    {
                        cursor: 'pointer;'
                    },
                    {
                        'background-repeat': 'no-repeat;'
                    },
                    {
                        'background-position': 'center;'
                    },
                    {
                        border: '2px solid white;'
                    }
                ]
            },
            text: 'Settings',
            icon: 'imagpath',
            hovertext: ''
        },

        {
            control: 'videosubtitle',
            active: false,
            styleCSS: {
                style: [
                    {
                        position: 'absolute;'
                    },
                    {
                        outline: 'none;'
                    },
                    {
                        left: '4.4%;'
                    },
                    {
                        height: '4.4%;'
                    },
                    {
                        width: '32.3%;'
                    },
                    {
                        display: 'block;'
                    },
                    {
                        'z-index': '1;'
                    },
                    {
                        top: '23.2%;'
                    },
                    {
                        opacity: '0.7;'
                    },
                    {
                        color: '#ffffff;'
                    },
                    {
                        'font-size': '1.667vw;'
                    }
                ]
            },
            text: 'S2 E1: Asguard�s Vengeance Into The Dark',
            icon: 'imagpath',
            hovertext: ''
        },

        {
            control: 'subtitle',
            active: false,
            styleCSS: {
                style: [
                    {
                        position: 'absolute;'
                    },
                    {
                        'font-weight': 'bold;'
                    },
                    {
                        outline: 'none;'
                    },
                    {
                        left: '5.1%;'
                    },
                    {
                        height: '6.7%;'
                    },
                    {
                        'border-radius': '5px;'
                    },
                    {
                        width: '16%;'
                    },

                    {
                        'background-repeat': 'no-repeat;'
                    },
                    // {
                    //    "display":"block;"
                    // },
                    {
                        'z-index': '1;'
                    },
                    {
                        top: '62.6%;'
                    },
                    {
                        'padding-bottom': '0.5%;'
                    },
                    {
                        'padding-top': '0.6%;'
                    },
                    {
                        cursor: 'pointer;'
                    },
                    {
                        'padding-left': '0.1%;'
                    },

                    {
                        'background-position': 'left;'
                    },

                    {
                        'background-image':
                            'url(player/assets/player_assets/ic-backbuttontv/icon_subtitles.png);'
                    },
                    {
                        color: '#ffffff;'
                    },
                    {
                        'font-size': '23px;'
                    },
                    // {
                    //    "font-family": "ProximaNova-Semibold;"
                    // },
                    {
                        'border-radius': '2px;'
                    },
                    {
                        'font-stretch': 'normal;'
                    },
                    {
                        'font-style': 'normal;'
                    },
                    {
                        'letter-spacing': 'normal;'
                    },
                    {
                        'background-size': '50px;'
                    }
                ]
            },
            text: 'Audio & Subtitles',
            icon: 'imagpath',
            hovertext: ''
        },

        {
            control: 'skipintro_button',
            active: true,
            styleCSS: {
                style: [
                    {
                        position: 'absolute;'
                    },

                    {
                        right: '3.5%;'
                    },
                    {
                        height: '6.7%;'
                    },

                    {
                        width: '10%;'
                    },

                    {
                        display: 'none;'
                    },
                    {
                        'z-index': '1;'
                    },
                    {
                        bottom: '3.3%;'
                    },
                    {
                        'background-color': 'rgba(0,0,0, 0.5);'
                    },
                    {
                        color: 'rgba(255, 255, 255, 0.8);'
                    },
                    {
                        'border-radius': '16px;'
                    },
                    {
                        cursor: 'pointer;'
                    },
                    {
                        'font-size': '16px;'
                    },
                    {
                        'font-family': 'jioType;'
                    }
                ]
            },
            text: '',
            icon: 'imagpath',
            hovertext: ''
        },
        {
            control: 'title',
            active: false,
            styleCSS: {
                style: [
                    {
                        top: '17.4%;'
                    },
                    {
                        'font-weight': 'bold;'
                    },
                    {
                        outline: 'none;'
                    },
                    {
                        position: 'absolute;'
                    },
                    {
                        left: '5.2%;'
                    },
                    {
                        'font-size': '40px;'
                    },
                    // {
                    //    "font-family":" ProximaNova-Bold;"
                    // },
                    {
                        'font-stretch': 'normal;'
                    },
                    {
                        'font-style': 'normal;'
                    },
                    {
                        'line-height': 'normal;'
                    },
                    {
                        'letter-spacing': 'normal;'
                    },
                    {
                        color: '#ffffff;'
                    }
                ]
            },
            text: 'Thor: The Dark World',
            icon: 'imagpath',
            hovertext: ''
        },

        {
            control: '4k',
            active: true,
            styleCSS: {
                style: [
                    {
                        position: 'absolute;'
                    },
                    {
                        'background-color': 'gray;'
                    },
                    {
                        display: 'none;'
                    },
                    {
                        height: '76px;'
                    },
                    {
                        left: '21%;'
                    },
                    {
                        bottom: '1.5%;'
                    },
                    {
                        width: '63px;'
                    },
                    {
                        'background-size': '65px 75px;'
                    },
                    {
                        'background-image':
                            'url(../../assets/img/4kBadge.png) !important; '
                    }
                ]
            },
            text: '',
            icon: 'imagpath',
            hovertext: ''
        },

        {
            control: 'seekbar',
            active: true,
            styleCSS: {
                style: [
                    {
                        bottom: '10%;'
                    },
                    {
                        'pointer-events': 'none;'
                    },
                    {
                        display: 'none;'
                    },
                    {
                        width: '75%;'
                    },
                    {
                        position: 'absolute;'
                    },
                    {
                        left: '14.5%;'
                    },
                    {
                        height: '0.5%!importent;'
                    },
                    {
                        outline: 'none'
                    },
                    {
                        right: '13%;'
                    },
                    {
                        'z-index': '2!important; '
                    },
                    {
                        'font-size': '0px;'
                    }
                ]
            },
            text: '',
            icon: 'imagpath',
            hovertext: ''
        },

        {
            control: 'customSeekBar',
            active: true,
            styleCSS: {
                style: [
                    {
                        bottom: '10.5%;'
                    },
                    {
                        display: 'none;'
                    },
                    {
                        'pointer-events': 'none;'
                    },
                    {
                        outline: 'none;'
                    },
                    {
                        width: '80%;'
                    },
                    {
                        position: 'absolute;'
                    },
                    {
                        left: '9.5%;'
                    },
                    {
                        height: '0.5%!importent;'
                    },
                    {
                        outline: 'none'
                    },
                    {
                        right: '13%;'
                    },
                    {
                        'z-index': '2!important; '
                    },
                    {
                        'font-size': '0px;'
                    }
                ]
            },
            text: '',
            icon: 'imagpath',
            hovertext: ''
        },

        {
            control: 'play_pause',
            active: true,
            styleCSS: {
                style: [
                    {
                        top: '85%;'
                    },
                    {
                        position: 'absolute;'
                    },

                    {
                        left: '3%;'
                    },
                    {
                        height: '7%;'
                    },
                    {
                        width: '4%;'
                    },
                    {
                        'background-size': '18px 31px;'
                    },

                    {
                        'background-image':
                            'url(../../assets/img/newPlayer_ipl/Play_ipl.svg);'
                    },
                    {
                        'background-color': 'black'
                    },
                    {
                        display: 'block;'
                    }
                ]
            },
            text: '',
            icon: 'imagpath',
            hovertext: ''
        },

        //.............................

        {
            control: 'controllbarv18',
            active: true,
            styleCSS: {
                style: [
                    {
                        top: '74%;'
                    },

                    {
                        position: 'absolute;'
                    },
                    {
                        left: '11.7%;'
                    },
                    {
                        height: '11.1%;'
                    },

                    {
                        width: '76.6%;'
                    },
                    {
                        display: 'none;'
                    }
                ]
            },
            text: '',
            icon: 'imagpath',
            hovertext: ''
        },

        {
            control: 'forwardButtonv18',
            active: true,
            styleCSS: {
                style: [
                    {
                        position: 'absolute;'
                    },

                    {
                        right: '36.7%;'
                    },
                    {
                        height: '58px;'
                    },
                    {
                        'background-image':
                            'url(../../assets/img/newPlayer_ipl/Forward_ipl.svg) !important; '
                    },

                    {
                        width: '60px;'
                    },
                    {
                        'background-size': '33px 33px;'
                    },

                    {
                        top: '45.6%;'
                    },
                    {
                        'background-color': 'rgba(255,255,255, 0.2);'
                    },
                    {
                        display: 'block;'
                    },
                    {
                        'border-radius': '50%;'
                    }
                ]
            },
            text: '',
            icon: 'imagpath',
            hovertext: ''
        },

        {
            control: 'rewindButtonv18',
            active: true,
            styleCSS: {
                style: [
                    {
                        position: 'absolute;'
                    },

                    {
                        left: '36.3%;'
                    },
                    {
                        height: '58px;'
                    },
                    {
                        'background-image':
                            'url(../../assets/img/newPlayer_ipl/Rewind_ipl.svg) !important; '
                    },

                    {
                        width: '60px;'
                    },
                    {
                        'background-size': '33px 33px;'
                    },
                    {
                        display: 'block !important;'
                    },
                    {
                        'background-color': 'rgba(255,255,255, 0.2);'
                    },
                    {
                        top: '45.44%;'
                    },
                    {
                        'border-radius': '50%;'
                    }
                ]
            },
            text: '',
            icon: 'imagpath',
            hovertext: ''
        },

        {
            control: 'settingButtonv18',
            active: true,
            styleCSS: {
                style: [
                    {
                        top: '6%;'
                    },
                    {
                        position: 'absolute;'
                    },
                    {
                        right: '1%;'
                    },
                    {
                        height: '7%;'
                    },
                    {
                        'border-radius': '10px;'
                    },
                    {
                        outline: 'none;'
                    },
                    {
                        width: '13.35%;'
                    },
                    {
                        display: 'none;'
                    },

                    {
                        transform: 'rotate(0deg);'
                    },
                    {
                        color: 'rgba(255, 255, 255, 0.8);'
                    },
                    {
                        cursor: 'pointer;'
                    },
                    {
                        'background-repeat': 'no-repeat;'
                    },
                    {
                        'background-position': 'center;'
                    },
                    {
                        border: '2px solid white;'
                    }
                ]
            },
            text: '',
            icon: 'imagpath',
            hovertext: ''
        },

        {
            control: 'allEpisodeButtonv18',
            active: true,
            styleCSS: {
                style: [
                    {
                        bottom: '19%;'
                    },
                    {
                        position: 'absolute;'
                    },
                    {
                        left: '85.5%;'
                    },
                    {
                        height: '7%;'
                    },
                    {
                        'border-radius': '10px;'
                    },
                    {
                        outline: 'none;'
                    },
                    {
                        width: '12.09%;'
                    },
                    {
                        display: 'none;'
                    },

                    {
                        transform: 'rotate(0deg);'
                    },
                    {
                        color: 'rgba(255, 255, 255, 0.8);'
                    },
                    {
                        cursor: 'pointer;'
                    },
                    {
                        'background-repeat': 'no-repeat;'
                    },
                    {
                        'background-position': 'center;'
                    },
                    {
                        border: '2px solid white;'
                    }
                ]
            },
            text: '',
            icon: 'imagpath',
            hovertext: ''
        },
        {
            control: 'PlayFromBeginning',
            active: true,
            styleCSS: {
                style: [
                    {
                        bottom: '19%;'
                    },
                    {
                        position: 'absolute;'
                    },
                    {
                        left: '2.5%;'
                    },
                    {
                        height: '7%;'
                    },
                    {
                        'border-radius': '10px;'
                    },
                    {
                        outline: 'none;'
                    },
                    {
                        width: '16.82%;'
                    },
                    {
                        display: 'none;'
                    },

                    {
                        transform: 'rotate(0deg);'
                    },
                    {
                        color: 'rgba(255, 255, 255, 0.8);'
                    },
                    {
                        cursor: 'pointer;'
                    },
                    {
                        'background-repeat': 'no-repeat;'
                    },
                    {
                        'background-position': 'center;'
                    },
                    {
                        border: '2px solid white;'
                    }
                ]
            },
            text: '',
            icon: 'imagpath',
            hovertext: ''
        },
        {
            control: 'keyMoment',
            active: true,
            styleCSS: {
                style: [
                    {
                        bottom: '19%;'
                    },
                    {
                        position: 'absolute;'
                    },
                    {
                        right: '2.5%;'
                    },
                    {
                        height: '7%;'
                    },
                    {
                        'border-radius': '10px;'
                    },
                    {
                        outline: 'none;'
                    },
                    {
                        width: '12.82%;'
                    },
                    {
                        display: 'none;'
                    },

                    {
                        transform: 'rotate(0deg);'
                    },
                    {
                        color: 'rgba(255, 255, 255, 0.8);'
                    },
                    {
                        cursor: 'pointer;'
                    },
                    {
                        'background-repeat': 'no-repeat;'
                    },
                    {
                        'background-position': 'center;'
                    },
                    {
                        border: '2px solid white;'
                    }
                ]
            },
            text: '',
            icon: 'imagpath',
            hovertext: ''
        },
        {
            control: 'multiCam',
            active: true,
            styleCSS: {
                style: [
                    {
                        position: 'absolute;'
                    },
                    {
                        'background-image':
                            'url(../../assets/img/newPlayer_ipl/sports_cricket.svg);'
                    },
                    {
                        'background-color': 'rgba(255,255,255, 0.2);'
                    },
                    {
                        right: '22%;'
                    },
                    {
                        top: '9.5%;'
                    },
                    {
                        height: '72px;'
                    },

                    {
                        width: '72px;'
                    },
                    {
                        display: 'none;'
                    },
                    {
                        'background-size': '34px 36px;'
                    },
                    {
                        'background-repeat': 'no-repeat;'
                    },
                    {
                        'background-position': 'center;'
                    },
                    {
                        'border-radius': '50%;'
                    }
                ]
            },
            text: '',
            icon: 'imagpath',
            hovertext: ''
        },
        {
            control: 'multiCamGoLive',
            active: true,
            styleCSS: {
                style: [
                    {
                        position: 'absolute;'
                    },
                    {
                        right: '2.5%;'
                    },
                    {
                        bottom: '3%;'
                    },
                    {
                        'font-size': '16px;'
                    },
                    {
                        height: '40px;'
                    },
                    {
                        'background-color': 'rgba(255,255,255, 0.2);'
                    },
                    {
                        'border-radius': '10px;'
                    },
                    {
                        width: '120px;'
                    },
                    {
                        display: 'none;'
                    }
                ]
            },
            text: 'Go Live',
            icon: 'imagpath',
            hovertext: ''
        },
        {
            control: 'multiCamLiveFeed',
            active: true,
            styleCSS: {
                style: [
                    {
                        position: 'absolute;'
                    },
                    {
                        right: '13.5%;'
                    },
                    {
                        bottom: '5.3%;'
                    },
                    {
                        'font-size': '16px;'
                    },
                    {
                        height: '40px;'
                    },
                    {
                        'background-color': 'rgba(255,255,255, 0.2);'
                    },
                    {
                        'border-radius': '100px;'
                    },
                    {
                        width: '120px;'
                    },
                    {
                        display: 'none;'
                    }
                ]
            },
            text: 'MAIN FEED',
            icon: 'imagpath',
            hovertext: ''
        },
        {
            control: 'subtitleButtonv18',
            active: true,
            styleCSS: {
                style: [
                    {
                        top: '6%;'
                    },
                    {
                        position: 'absolute;'
                    },
                    {
                        left: '67%;'
                    },
                    {
                        height: '7%;'
                    },
                    {
                        'border-radius': '10px;'
                    },
                    {
                        outline: 'none;'
                    },
                    {
                        width: '16.82%;'
                    },
                    {
                        display: 'none;'
                    },

                    {
                        transform: 'rotate(0deg);'
                    },
                    {
                        color: 'rgba(255, 255, 255, 0.8);'
                    },
                    {
                        cursor: 'pointer;'
                    },
                    {
                        'background-repeat': 'no-repeat;'
                    },
                    {
                        'background-position': 'center;'
                    },
                    {
                        border: '2px solid white;'
                    }
                ]
            },
            text: '',
            icon: 'imagpath',
            hovertext: ''
        },
        {
            control: 'audioButtonv18',
            active: true,
            styleCSS: {
                style: [
                    {
                        top: '6%;'
                    },
                    {
                        position: 'absolute;'
                    },
                    {
                        left: '48%;'
                    },
                    {
                        height: '7%;'
                    },
                    {
                        'border-radius': '10px;'
                    },
                    {
                        outline: 'none;'
                    },
                    {
                        width: '16.82%;'
                    },
                    {
                        display: 'none;'
                    },

                    {
                        transform: 'rotate(0deg);'
                    },
                    {
                        color: 'rgba(255, 255, 255, 0.8);'
                    },
                    {
                        cursor: 'pointer;'
                    },
                    {
                        'background-repeat': 'no-repeat;'
                    },
                    {
                        'background-position': 'center;'
                    },
                    {
                        border: '2px solid white;'
                    }
                ]
            },
            text: '',
            icon: 'imagpath',
            hovertext: ''
        },
        {
            control: 'informationButtonv18',
            active: true,
            styleCSS: {
                style: [
                    {
                        position: 'absolute;'
                    },
                    {
                        left: '29.2%;'
                    },
                    {
                        height: '10.5%;'
                    },

                    {
                        width: '5.974%;'
                    },

                    {
                        top: '74.6%;'
                    }
                ]
            },
            text: '',
            icon: 'imagpath',
            hovertext: ''
        },
        //..........................
        {
            control: 'videoQuality',
            active: false,
            styleCSS: {
                style: [
                    {
                        top: '88%;'
                    },
                    {
                        position: 'absolute;'
                    },
                    {
                        outline: 'none;'
                    },
                    {
                        left: '81%;'
                    },
                    {
                        height: '10%;'
                    },
                    {
                        width: '10%;'
                    },
                    {
                        display: 'none;'
                    },
                    {
                        transform: 'rotate(0deg);'
                    },
                    {
                        transform: 'transform 500ms linear;'
                    },
                    {
                        'z-index': '1;'
                    },
                    {
                        cursor: 'pointer;'
                    },
                    {
                        right: '24rem;'
                    },
                    {
                        bottom: '45%;'
                    },
                    {
                        width: '6em;'
                    },
                    {
                        right: ' 1rem;'
                    },
                    {
                        cursor: 'pointer;'
                    },
                    {
                        'background-repeat': 'no-repeat;'
                    },
                    {
                        'background-position': 'center;'
                    },
                    {
                        'background-image':
                            'url(UIComponent/Component/assets/LogiPlayer_icons/ic-settings/ic-settings.png);'
                    }
                ]
            },
            text: '',
            icon: 'imagpath',
            hovertext: ''
        },

        {
            control: 'backbutton',
            active: false,
            styleCSS: {
                style: [
                    {
                        top: '9.3%;'
                    },
                    {
                        position: 'absolute;'
                    },
                    {
                        outline: 'none;'
                    },
                    {
                        left: '5.2%;'
                    },
                    {
                        height: '4.6%;'
                    },
                    {
                        width: '2.8%;'
                    },
                    {
                        display: 'block;'
                    },
                    {
                        transform: 'rotate(0deg);'
                    },
                    {
                        transform: 'transform 500ms linear;'
                    },
                    {
                        'z-index': '1;'
                    },
                    {
                        cursor: 'pointer;'
                    },
                    {
                        right: '24rem;'
                    },
                    {
                        bottom: '45%;'
                    },

                    {
                        cursor: 'pointer;'
                    },
                    {
                        'background-repeat': 'no-repeat;'
                    },
                    {
                        'background-position': 'center;'
                    },
                    {
                        'background-image': 'url(assets/img/ic_back.png);'
                    }
                ]
            },
            text: '',
            icon: 'imagpath',
            hovertext: ''
        },
        {
            control: 'videotitle',
            active: true,
            styleCSS: {
                style: [
                    {
                        top: '3.5%;'
                    },
                    {
                        left: '5.5%;'
                    },
                    {
                        'font-size': '1.56vw;'
                    },
                    {
                        position: 'absolute;'
                    },
                    {
                        outline: 'none;'
                    },
                    {
                        height: '10%;'
                    },
                    {
                        width: '100%;'
                    },
                    {
                        display: 'none;'
                    },
                    {
                        transform: 'rotate(0deg);'
                    },
                    {
                        transform: 'transform 500ms linear;'
                    },
                    {
                        'z-index': '1;'
                    },
                    {
                        cursor: 'pointer;'
                    },
                    {
                        cursor: 'pointer;'
                    },
                    {
                        'background-repeat': 'no-repeat;'
                    },
                    {
                        'background-position': 'center;'
                    },
                    {
                        'font-family': 'ProximaNova-Bold'
                    }
                ]
            },
            text: '',
            icon: 'imagpath',
            hovertext: ''
        },
        {
            control: 'AgeRating',
            active: true,
            styleCSS: {
                style: [
                    {
                        top: '4.4%;'
                    },
                    {
                        position: 'absolute;'
                    },
                    {
                        outline: 'none;'
                    },
                    {
                        left: '2.5%;'
                    },
                    {
                        width: '47%;'
                    },
                    {
                        display: 'none;'
                    },
                    {
                        transform: 'rotate(0deg);'
                    },
                    {
                        transform: 'transform 500ms linear;'
                    },
                    {
                        'z-index': '1;'
                    },
                    {
                        cursor: 'pointer;'
                    },
                    {
                        cursor: 'pointer;'
                    },
                    {
                        'background-repeat': 'no-repeat;'
                    },
                    {
                        'background-position': 'center;'
                    },
                    {
                        'font-family': 'ProximaNova-Bold'
                    }
                ]
            },
            text: '',
            icon: 'imagpath',
            hovertext: ''
        },
        {
            control: 'duration',
            active: true,
            styleCSS: {
                style: [
                    {
                        display: 'none;'
                    },
                    {
                        top: '88%;'
                    },
                    {
                        left: '92%;'
                    },
                    {
                        position: 'absolute;'
                    }
                    //   {
                    //       "opacity":"0.5;"
                    //   },
                    //   {
                    //      "font-size":"1.1713vw;"
                    //   },
                    //   {
                    //      "font-family":"Rubik-Regular"
                    //   }
                ]
            },
            text: '',
            icon: 'imagpath',
            hovertext: ''
        },
        {
            control: 'remainingTimeDisplay',
            active: false,
            styleCSS: {
                style: [
                    {
                        display: 'block;'
                    },
                    {
                        top: '90.2%;'
                    },
                    {
                        left: '6%;'
                    },
                    {
                        position: 'absolute;'
                    },
                    {
                        'font-size': '18px;'
                    }
                ]
            },
            text: '',
            icon: 'imagpath',
            hovertext: ''
        },
        {
            control: 'timeDivider',
            active: false,
            styleCSS: {
                style: [
                    {
                        display: 'block;'
                    },
                    {
                        top: '87%;'
                    },
                    {
                        left: '89.3%;'
                    },
                    {
                        position: 'absolute;'
                    },
                    {
                        'font-size': '24px;'
                    },
                    {
                        opacity: '0.5'
                    }
                ]
            },
            text: '',
            icon: 'imagpath',
            hovertext: ''
        },
        {
            control: 'waterMark',
            active: false,
            styleCSS: {
                style: [
                    {
                        top: '87%;'
                    },
                    {
                        position: 'absolute;'
                    },
                    {
                        left: '4.7%;'
                    },
                    {
                        height: '42px;'
                    },
                    {
                        width: '120px;'
                    },
                    {
                        display: 'none;'
                    },
                    {
                        'background-image':
                            'url(../../assets/img/vootlogo.png); '
                    }
                ]
            },
            text: '',
            icon: 'imagpath',
            hovertext: ''
        },
        {
            control: 'liveWaterMark',
            active: false,
            styleCSS: {
                style: [
                    {
                        top: '3.6%;'
                    },
                    {
                        position: 'absolute;'
                    },
                    {
                        left: '89%;'
                    },
                    {
                        height: '60px;'
                    },
                    {
                        width: '120px;'
                    },
                    {
                        display: 'none;'
                    },
                    {
                        'background-image':
                            'url(../../assets/img/liveImage.png); '
                    }
                ]
            },
            text: '',
            icon: 'imagpath',
            hovertext: ''
        },
        {
            control: 'currentTime',
            active: true,
            styleCSS: {
                style: [
                    {
                        display: 'none;'
                    },
                    {
                        top: '88%;'
                    },
                    {
                        left: '6.5%;'
                    },
                    {
                        position: 'absolute;'
                    }
                    //   {
                    //      "font-size":"1.1713vw;"
                    //   },
                    //   {
                    //    "font-family":"Rubik-Regular;"
                    //   }
                ]
            },
            text: '',
            icon: 'imagpath',
            hovertext: ''
        }
    ]
}

export default jsonData
