import { useCallback, useState, useEffect, useRef } from 'react'
import jsonData from '../playerjson'

export default function PlayerInit({ playBackdata }) {
    let logixPlayer
    const playerBuilderRef = useRef(null)

    const [playerData, setPlayerData] = useState()

    useEffect(() => {
        setPlayerData(playBackdata)
    }, [playBackdata])

    useEffect(() => {
        if (!logixPlayer) {
            logixPlayer = new window.LogixPlayerComponent()
            createPlayerObject(logixPlayer)
        }
        console.log('logixplayer', logixPlayer)
    }, [logixPlayer])

    const createPlayerObject = useCallback((logixPlayer) => {
        logixPlayer.createPlayer(jsonData)
    }, [])

    useEffect(() => {
        var playBackData = {
            autoplay: true,
            videoUrl:
                'https://sosnm1.shakticloud.ai:9024/immersobuk01/VFS/results/0a88f841-71cc-4cb7-a236-48a65726c0c2/result.mp4?response-content-disposition=inline&AWSAccessKeyId=immersouser&Signature=8t6qLDIkDzJ7g3mXm13w8dzn4X8%3D&Expires=**********', //Mandatory
            viacom_mux_env_key_dev: '',
            fromplayerError: '',
            appVersion: '',
            licenseUrl: '', //Mandatory if it is a DRM content, application must pass this url
            certificateUrl: '', //Mandatory if it is a DRM content
            playerControlsLayout: jsonData,
            // continueWatchingStartTime:
            //     playbackdata[0]?.continueWatchingStartTime,
            jioAdsConfig: '',
            guestusertoken: '',
            isOverrideNative: false,
            liveCurrentTime: 0,
            qualityCapping: 0,
            // qualityCapping: 1280,
            isjioadsServed: false,
            // isjioadsServed: false,
            isMidJioadsServed: false,
            // isMidJioadsServed: false,
            jioAds: '',
            // jioAds:"8b9rmogu",
            jioAdsMidrollID: '',
            // jioAdsMidrollID: '8b9rmogu',
            midrollCueTimes: '',
            midrollUid: 'midrollUid',
            midAdDataContainerId: 'midrollId',
            jioAdsMidrollAdsId: '',
            prerollUid: '',
            adsClassName: '',
            adDataContainerId: '',
            dataSource:
                navigator.userAgent.indexOf('Tizen') > -1
                    ? 'com.viacom18.media.ondemand.tizen'
                    : 'com.viacom18.media.ondemand.webos',
            // midrollInterval:600,
            midrollInterval: '',
            oldJioAsset: '',
            // playbackResponseData: playbackdata,
            playerCurrentTimeForSwitchCotent: false,
            DRMHeaders: '',
            vendorName: 'logituit_'
        }

        var assetMetaData = {
            isLive: false, //Mandatory is content VOD or LIVE
            thumbnail_stream_url:
                'https://www.radiantmediaplayer.com/media/vtt/thumbnails/bbb-thumbnails.vtt',
            isDvr: false,
            isEncrypted: false, //Mandatory is content DRM or NON-DRM
            isControlDisabled: false,
            isContinueWatch: false, //Mandatory
            thumbnailUrl: '',
            watermarkUrl: '',
            controlsDisappearTimerValuetv: 5,
            mediaType: '',
            mediaTypeLabels: '',
            contentDetails: '',
            maxAllowedBitrate: -1,
            initialBitrate: 600,
            maxAudioAutoBitrate: 64
        }

        // var TvplayerConfig = {
        //     videoQualityConfig: videoQualityConfigSetting,
        //     defaultVideoQualityBitrate: 600,
        //     forwardRewindTime: 10,
        //     controlsDisappearTimerValue: 3
        // }

        // var adsConfig = {}
        // try {
        //     adsConfig = {
        //         enabled: playbackdata[0]?.enabled === true ? true : false,
        //         adsUrl: playbackdata[0]?.adsUrl
        //     }
        // } catch (e) {
        //     console.error(e)

        //     adsConfig = {
        //         enabled: false,
        //         adsUrl: ''
        //     }
        // }

        if (playBackData.videoUrl.indexOf('mp4') > -1) {
            playBackData.type = 'video/mp4'
        } else if (playBackData.videoUrl.indexOf('mpd') > -1) {
            playBackData.type = 'application/dash+xml'
        } else if (this.playbackData.videoUrl.indexOf('m3u8') > -1) {
            playBackData.type = 'application/x-mpegURL'
        }

        // playerBuilderRef = new window.playerBuilder()
        // playerBuilderRef.setPlaybackData(playBackData)
        // playerBuilderRef.setAssetMetaData(assetMetaData)
        playerBuilderRef.current = new window.playerBuilder()
        playerBuilderRef.current.setPlaybackData(playBackData)
        playerBuilderRef.current.setAssetMetaData(assetMetaData)
        // playerBuilderRef.setPlayerConfig(TvplayerConfig)
        // playerBuilderRef.setAdsConfig(adsConfig)

        if (
            logixPlayer &&
            document.getElementsByClassName('mainVideoContainer').length === 0
        )
            logixPlayer
                .initializePlayer('logixPlayer', playerBuilderRef.current)
                .then(function (playerobj) {
                    // var me = this;
                })
        return (playerobj) => {
            if (playerBuilderRef.current?.destroy) {
                playerBuilderRef.current.destroy()
            }
        }
    }, [playerData])
}
