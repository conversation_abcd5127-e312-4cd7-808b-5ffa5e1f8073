import styled from 'styled-components'

export const SafeArea = styled.div`
    background: #0a0a0a;
    min-height: 100vh;
    width: 100vw;
    box-sizing: border-box;
    padding-bottom: 32px;
    color: #f5f5f5;
    @media (min-width: 600px) {
        max-width: 430px;
        margin: 0 auto;
        border-radius: 24px;
        box-shadow: 0 0 24px 0 #0008;
    }
`

export const Header = styled.div`
    display: flex;
    align-items: center;
    padding: 0 0 12px 0;
    margin: 0 0 0 0;
`

export const BackArrow = styled.span`
    font-size: 2.2rem;
    margin-left: 18px;
    margin-right: 18px;
    cursor: pointer;
    color: #f5f5f5;
`

export const Title = styled.h1`
    font-size: 2.1rem;
    font-weight: 500;
    margin: 0;
    color: #f5f5f5;
`

export const SectionTitle = styled.h2`
    font-size: 1.08rem;
    font-weight: 700;
    margin: 28px 0 10px 18px;
    color: #e0e0e0;
    letter-spacing: 0.01em;
`

export const Card = styled.div`
    background: #191919;
    border-radius: 22px;
    margin: 0 14px 18px 14px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
`

export const CardRow = styled.div`
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 22px 18px 22px 18px;
    border-bottom: 1px solid #232323;
    font-size: 1.18rem;
    cursor: pointer;
    color: #f5f5f5;
    &:last-child {
        border-bottom: none !important;
    }
`

export const CardRowRight = styled.span`
    color: #bdbdbd;
    font-size: 1.08rem;
`

export const SaveButton = styled(CardRow)`
    justify-content: flex-start;
    color: #f5f5f5;
    font-weight: 500;
`
