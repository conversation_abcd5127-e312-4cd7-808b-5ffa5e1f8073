import {
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,
    Title,
    SectionTitle,
    Card,
    CardRow,
    CardRowRight,
    SaveButton
} from './style'

const Settings = () => {
    // Placeholder navigation handlers
    const handleNav = (label) => alert(`Navigate to ${label}`)

    return (
        <SafeArea>
            <Header>
                <BackArrow onClick={() => handleNav('Back')}>&larr;</BackArrow>
                <Title>Settings</Title>
            </Header>

            <SectionTitle>Account</SectionTitle>
            <Card>
                <CardRow onClick={() => handleNav('Change Email')}>
                    Change Email <CardRowRight>&#8250;</CardRowRight>
                </CardRow>
                <CardRow onClick={() => handleNav('Change Password')}>
                    Change Password <CardRowRight>&#8250;</CardRowRight>
                </CardRow>
                <CardRow onClick={() => handleNav('Privacy')}>
                    Privacy <CardRowRight>&#8250;</CardRowRight>
                </CardRow>
            </Card>

            <SectionTitle>Subscription & Billing</SectionTitle>
            <Card>
                <CardRow onClick={() => handleNav('Current Plan')}>
                    Current Plan <CardRowRight>Creator+ &#8250;</CardRowRight>
                </CardRow>
                <CardRow onClick={() => handleNav('Payment Methods')}>
                    Payment Methods <CardRowRight>&#8250;</CardRowRight>
                </CardRow>
                <CardRow onClick={() => handleNav('Billing History')}>
                    Billing History <CardRowRight>&#8250;</CardRowRight>
                </CardRow>
            </Card>

            <SectionTitle>Preferences</SectionTitle>
            <Card>
                <CardRow onClick={() => handleNav('Theme')}>
                    Theme <CardRowRight>Dark &#8250;</CardRowRight>
                </CardRow>
                <SaveButton onClick={() => handleNav('Save Settings')}>
                    Save Settings <CardRowRight>&#8250;</CardRowRight>
                </SaveButton>
            </Card>

            <SectionTitle>Notifications</SectionTitle>
            <Card>
                <CardRow onClick={() => handleNav('Email')}>
                    Email <CardRowRight>&#8250;</CardRowRight>
                </CardRow>
            </Card>
        </SafeArea>
    )
}

export default Settings
