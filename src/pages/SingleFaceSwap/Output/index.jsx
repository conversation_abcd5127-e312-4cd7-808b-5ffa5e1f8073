import { AiOutlineDislike, AiOutlineLike } from 'react-icons/ai'
import { BsArrowRepeat, BsStars } from 'react-icons/bs'
import { useEffect, useState } from 'react'

import { Button } from '../../../components'
import DoMoreModal from './DoMoreModal'
import Loader from '../../../components/UI/Loader'
import { LuDownload } from 'react-icons/lu'
import { universe } from '../../../assets/index'
import { useNavigate } from 'react-router-dom'
import { useSelector } from 'react-redux'

const Output = () => {
    const { output_url } = useSelector((state) => state.session)
    const [showDoMore, setShowDoMore] = useState(false)
    const [imageLoaded, setImageLoaded] = useState(false)
    const [imageError, setImageError] = useState(false)
    const [retryCount, setRetryCount] = useState(0)
    const navigate = useNavigate();

    useEffect(() => {
        if (imageError && retryCount < 10) {
            const timer = setTimeout(() => {
                setImageError(false)
                setRetryCount(retryCount + 1)
            }, 2000)
            return () => clearTimeout(timer)
        }
    }, [imageError, retryCount])

    return (
        <div className="flex flex-col min-h-screen bg-black text-white">
            {/* Main Image with Watermark */}
            <div className="flex-1 flex flex-col items-center justify-center">
                <div className="relative w-full overflow-hidden">
                    {/* Floating Button Overlay */}
                    <Button
                        style={{
                            position: 'absolute',
                            top: 0,
                            right: 0,
                            zIndex: 10,
                            padding: '8px 16px',
                            fontSize: 16,
                            borderRadius: 24,
                            opacity: 0.95,
                            backgroundColor: 'transparent',
                        }}
                        onClick={() => navigate('/create')}
                    >
                        x
                    </Button>
                    {!imageLoaded && (
                        <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-80 z-10">
                            <Loader show />
                        </div>
                    )}
                    <img
                        key={retryCount} // force reload on retry
                        src={output_url}
                        alt="Face Swap Output"
                        className="w-full h-full object-cover"
                        style={{ display: imageLoaded ? 'block' : 'none' }}
                        onLoad={() => setImageLoaded(true)}
                        onError={() => setImageError(true)}
                    />
                    {/* Watermark/Logo */}
                    <img
                        src={universe}
                        alt="Eros Universe Logo"
                        className="absolute top-2 right-2 w-20 opacity-90"
                    />
                    {/* Disclaimer */}
                    <div className="px-4 py-2 text-xs text-gray-300 bg-black">
                        Disclaimer: This face swap is AI-generated for creative
                        use only. The tool owners do not claim rights over the
                        original images or likenesses used.
                    </div>
                </div>
            </div>

            {/* Action Icons */}
            <div className="flex items-center justify-between px-6 pb-12 bg-black text-gray-300">
                <div className=" flex gap-6">
                    <AiOutlineLike size={24} />
                    <AiOutlineDislike size={24} />
                    <BsArrowRepeat size={24} />
                    <LuDownload size={24} />
                </div>
                <div
                    className="flex gap-1 items-center text-[#00B8F8]"
                    onClick={() => setShowDoMore(true)}
                >
                    <BsStars size={24} />
                    Do More
                </div>
            </div>

            {/* Share and Publish Buttons */}
            <div className="flex items-center justify-between px-4 py-4 bg-black">
                <button className="flex-1 mr-2 py-2 rounded-full border border-[#00B8F8] text-[#00B8F8] font-semibold text-lg hover:bg-[#00B8F8]/25 transition">
                    Share
                </button>
                <button className="flex-1 ml-2 py-2 rounded-full bg-[#00B8F8] text-black font-semibold text-lg hover:bg-[#00B8F8]/55 transition">
                    Publish
                </button>
            </div>

            <DoMoreModal
                open={showDoMore}
                onClose={() => setShowDoMore(false)}
            />
        </div>
    )
}

export default Output
