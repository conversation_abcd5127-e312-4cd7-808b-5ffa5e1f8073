import { resetGenerationSession, updateGenerationSession } from '../../redux/Reducers/generationSessionReducer'

import AssetMarker from '../../components/UI/AssetMarker'
import { CONTENT_TYPE } from '../../utils/constants'
import { useDispatch } from 'react-redux'
import { useNavigate } from 'react-router-dom'

const TempCard = ({ item }) => {
    const navigate = useNavigate()
    const dispatch = useDispatch()

    const handleCardClick = () => {
        console.log(`You Tapped: ${JSON.stringify(item)}`)

        const template_id = item.template_id

        dispatch(resetGenerationSession())
        dispatch(
            updateGenerationSession({
                template_id,
                thumbnail_url: item.signed_template_url,
                content_type: CONTENT_TYPE.IMAGE
            })
        )
        navigate(`/source`)
    }

    return (
        <div
            key={item.template_id}
            className="w-44 h-44 flex-shrink-0 rounded-2xl bg-[#23243a] shadow-md overflow-hidden border border-gray-100 border-opacity-20 relative"
            onClick={handleCardClick}
        >
            <img
                src={item.signed_template_url}
                alt={`thumbnail image for file ${item.filename}`}
                className="w-full h-full object-cover"
            />

            {/* image or video tag with overlay */}
            <AssetMarker content_type={CONTENT_TYPE.IMAGE} />
        </div>
    )
}

export default TempCard
