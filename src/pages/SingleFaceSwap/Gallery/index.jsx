import { useNavigate, useParams } from 'react-router-dom'

import { FaArrowLeft } from 'react-icons/fa'
import { Loader } from '../../../components/UI'
import SuggestedRailCard from '../../../components/Card/SuggestedRailCard'
import TempCard from '../TempCard'
import { useGetAllFSTemplates } from '../../../Hooks/useFaceSwap'
import { useGetContentMeta } from '../../../Hooks/useCreatePage'

const PhotoGallery = () => {
    const navigate = useNavigate()
    const { query } = useParams()

    const { data: RQtemplates, isLoading } = useGetAllFSTemplates()
    const {
        data: contentGallery,
        isLoading: contentGalleryLoading,
        isSuccess: contentGallerySuccess
    } = useGetContentMeta({ query })

    return (
        <div className="min-h-screen bg-black flex flex-col">
            {/* Header */}
            <div className="flex items-center px-4 pt-6 pb-2">
                <button
                    onClick={() => navigate(-1)}
                    className="text-white mr-3"
                >
                    <FaArrowLeft size={20} />
                </button>
                <span className="text-white text-lg font-medium">{query ?? "Default"}</span>
            </div>

            {/* Gallery grid */}
            <div className="flex-1 overflow-y-auto px-3 pb-4 mx-auto mt-4">
                <div className="grid grid-cols-2 gap-3">
                    {contentGallerySuccess && contentGallery?.data?.length > 0
                        ? contentGallery?.data?.map((item) => (
                              <SuggestedRailCard item={item} key={item.id} />
                          ))
                        : RQtemplates &&
                          RQtemplates?.available_templates?.map((img) => (
                              <TempCard item={img} key={img.template_id} />
                          ))}
                </div>
            </div>

            <Loader show={isLoading || contentGalleryLoading} />
        </div>
    )
}

export default PhotoGallery
