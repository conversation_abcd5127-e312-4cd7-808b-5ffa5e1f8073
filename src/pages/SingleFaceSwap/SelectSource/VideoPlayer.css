/* Video player styles for SelectSource component */

/* Portrait video container with cover mode */
.video-container-portrait {
  position: relative;
  width: 90vw;
  max-width: 28rem; /* max-w-md equivalent */
  aspect-ratio: 380/450; /* Portrait aspect ratio */
  border-radius: 16px;
  overflow: hidden;
  background: #404040;
}

/* Video styling for cover mode (default) */
.video-container-portrait video {
  width: 100%;
  height: 100%;
  object-fit: cover; /* Cover mode - fills container while maintaining aspect ratio */
  border-radius: 16px;
  pointer-events: none; /* Make video non-interactive */
}

/* For default aspect ratio (contain mode) */
.video-container-portrait.default-aspect video {
  object-fit: contain; /* Contain mode - shows full video with letterboxing if needed */
}

/* Ensure video plays smoothly on mobile */
.video-container-portrait video::-webkit-media-controls {
  display: none !important;
}

.video-container-portrait video::-webkit-media-controls-panel {
  display: none !important;
}

.video-container-portrait video::-webkit-media-controls-play-button {
  display: none !important;
}

.video-container-portrait video::-webkit-media-controls-start-playback-button {
  display: none !important;
}

/* Firefox video controls */
.video-container-portrait video::-moz-media-controls {
  display: none !important;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .video-container-portrait {
    width: 95vw;
  }
}

/* Loading state */
.video-container-portrait video[poster] {
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}
