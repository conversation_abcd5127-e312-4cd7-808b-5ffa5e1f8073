import './VideoPlayer.css'

import { useDispatch, useSelector } from 'react-redux'
import {
    useGetFSTemplateInfo,
    useUploadFSTemplate
} from '../../../Hooks/useFaceSwap'

import AssetMarker from '../../../components/UI/AssetMarker'
import { CONTENT_TYPE } from '../../../utils/constants'
import { FaArrowLeft } from 'react-icons/fa'
import Loader from '../../../components/UI/Loader'
import { updateGenerationSession } from '../../../redux/Reducers/generationSessionReducer'
import { useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { useToast } from '../../../components/UI'

const SelectSource = () => {
    const navigate = useNavigate()
    const dispatch = useDispatch()
    const user = useSelector((state) => state.auth.user)
    const { showError } = useToast()
    const { template_id, content_type, thumbnail_url, file_url } = useSelector(
        (state) => state.session
    )

    const { isLoading: templateInfoLoading, error: templateInfoError } =
        useGetFSTemplateInfo({ template_id, content_type })
    const {
        mutateAsync: uploadTemplateAsync,
        isPending: uploadTemplatePending
    } = useUploadFSTemplate()

    useEffect(() => {
        if (!template_id || !thumbnail_url) {
            navigate('/')
        }
    }, [template_id, thumbnail_url])

    const handleTemplateUpload = async () => {
        try {
            console.log('Selected Template:', template_id)
            const user_id =
                content_type === CONTENT_TYPE.VIDEO
                    ? 'f519338d-442a-4613-af8d-04a02ef06fcd'
                    : user.id

            const formData = new FormData()
            formData.append('template_id', template_id)
            if (user && user.id) {
                formData.append('user_id', user_id)
            }
            await uploadTemplateAsync(
                { data: formData, content_type },
                {
                    onSuccess: (data) => {
                        const detected_face_urls =
                            data?.signed_detected_face_urls ||
                            (data?.detected_faces_urls &&
                            Array.isArray(data.detected_faces_urls)
                                ? data.detected_faces_urls
                                : Object.values(data.detected_faces_urls))

                        console.log(
                            `detected_face_urls in select source: [${detected_face_urls}]`
                        )
                        console.log(`data in select source: ${JSON.stringify(data)}`);
                        
                        dispatch(
                            updateGenerationSession({
                                generation_id: data.generation_id,
                                detected_face_urls: detected_face_urls,
                                template_face_count: detected_face_urls.length
                            })
                        )
                        console.log('navigating to studio')
                        navigate(`/studio`)
                    }
                }
            )
        } catch (error) {
            showError(error)
        }
    }

    if (content_type === CONTENT_TYPE.VIDEO) {
        return (
            <div className="min-h-screen bg-black flex flex-col">
                {/* Header */}
                <div className="flex items-center px-4 pt-6 pb-2">
                    <button
                        onClick={() => navigate(-1)}
                        className="text-white mr-3"
                    >
                        <FaArrowLeft size={20} />
                    </button>
                    <span className="text-white text-lg font-medium">
                        Choose source
                    </span>
                </div>

                {/* Video */}
                <div className="flex-1 flex flex-col items-center justify-center">
                    <div className="relative video-container-portrait">
                        {file_url ? (
                            <video
                                src={file_url}
                                poster={thumbnail_url}
                                autoPlay
                                loop
                                muted
                                playsInline
                                preload="metadata"
                            >
                                Your browser does not support the video tag.
                            </video>
                        ) : (
                            <img
                                src={thumbnail_url}
                                alt="Source"
                                className="w-full h-full object-cover"
                            />
                        )}
                        {/* Video/Image icon overlay */}
                        <AssetMarker content_type={content_type} />
                    </div>
                </div>

                {/* Select button */}
                <div className="flex justify-center pb-8">
                    <button
                        disabled={uploadTemplatePending || templateInfoLoading}
                        className="bg-[#00bfff] text-white rounded-full px-10 py-2 text-base font-semibold shadow-md active:scale-95 transition"
                        onClick={handleTemplateUpload}
                    >
                        Select
                    </button>
                </div>

                <Loader show={templateInfoLoading || uploadTemplatePending} />
            </div>
        )
    }

    return (
        <div className="min-h-screen bg-black flex flex-col">
            {/* Header */}
            <div className="flex items-center px-4 pt-6 pb-2">
                <button
                    onClick={() => navigate(-1)}
                    className="text-white mr-3"
                >
                    <FaArrowLeft size={20} />
                </button>
                <span className="text-white text-lg font-medium">
                    Choose source
                </span>
            </div>

            {/* Image */}
            <div className="flex-1 flex flex-col items-center justify-center">
                <div className="relative w-[90vw] max-w-md aspect-[380/450] rounded-2xl overflow-hidden bg-neutral-800">
                    <img
                        src={thumbnail_url}
                        alt="Source"
                        className="w-full h-full object-cover"
                    />
                    {/* Image icon overlay */}
                    <AssetMarker content_type={content_type} />
                </div>
            </div>

            {/* Select button */}
            <div className="flex justify-center pb-8">
                <button
                    disabled={uploadTemplatePending || templateInfoLoading}
                    className="bg-[#00bfff] text-white rounded-full px-10 py-2 text-base font-semibold shadow-md active:scale-95 transition"
                    onClick={handleTemplateUpload}
                >
                    Select
                </button>
            </div>

            <Loader show={templateInfoLoading || uploadTemplatePending} />
        </div>
    )
}

export default SelectSource
