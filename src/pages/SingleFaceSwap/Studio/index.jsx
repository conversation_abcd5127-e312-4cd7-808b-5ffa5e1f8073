import { Bottomsheet, SuggestedRailCard } from '../../../components'
import { FaArrowLeft, FaArrowRight } from 'react-icons/fa'
import { useDispatch, useSelector } from 'react-redux'
import { useEffect, useState } from 'react'
import {
    useFaceSwap,
    useGetFSVideoStatus,
    useUploadFSTarget
} from '../../../Hooks/useFaceSwap'

import { CONTENT_TYPE } from '../../../utils/constants'
import Loader from '../../../components/UI/Loader'
import { MdAddAPhoto } from 'react-icons/md'
import { updateGenerationSession } from '../../../redux/Reducers/generationSessionReducer'
import { useNavigate } from 'react-router-dom'
import { useToast } from '../../../components/UI'

const Studio = () => {
    const navigate = useNavigate()
    const dispatch = useDispatch()
    const { showError } = useToast()

    const user = useSelector((state) => state.auth.user)
    const [uploadedImages, setUploadedImages] = useState([])
    const [showLoading, setShowLoading] = useState(false)
    const [showGuidelines, setShowGuidelines] = useState(false)
    const [pendingUploadIdx, setPendingUploadIdx] = useState(null)
    const [shouldFetchVideoStatus, setShouldFetchVideoStatus] = useState(false)

    const allImagesUploaded = () =>
        uploadedImages.length > 0 &&
        uploadedImages.every((image) => image !== null)

    const {
        template_id,
        thumbnail_url,
        detected_face_urls,
        template_face_count,
        generation_id,
        content_type
    } = useSelector((state) => state.session)
    const { mutateAsync: uploadTargetMutate, isSuccess: uploadTargetSuccess } =
        useUploadFSTarget()
    const { mutateAsync: swapFaceAsync } = useFaceSwap()
    const {
        data: videoStatus,
        isFetching: isFetchingVideoStatus,
        isSuccess: videoStatusSuccess,
        isError: videoStatusError
    } = useGetFSVideoStatus({ generation_id, shouldFetchVideoStatus })

    const handleImageUpload = (file, idx) => {
        setUploadedImages((prev) => {
            const updated = [...prev]
            updated[idx] = file ?? null
            return updated
        })
    }

    // Initialize uploadedImages array based on template_face_count
    useEffect(() => {
        setUploadedImages(Array(template_face_count).fill(null))
    }, [template_face_count])

    useEffect(() => {
        if (videoStatusSuccess) {
            setShowLoading(false)
            dispatch(
                updateGenerationSession({ output_url: videoStatus.output_url })
            )
            navigate('/output')
        } else if (isFetchingVideoStatus) {
            setShowLoading(true)
        }
    }, [videoStatusSuccess, isFetchingVideoStatus])

    const handleFaceSwap = async () => {
        if (allImagesUploaded) {
            console.log('All images uploaded, calling uploadTargetMutate')
            try {
                if (!uploadTargetSuccess) await uploadAllTargets()
                console.log('All targets uploaded successfully')
            } catch (error) {
                console.error('Error uploading targets:', error)
                showError(error)
                return
            }

            // call face swap api
            const indices = Array.from(
                { length: template_face_count },
                (_, index) => index
            )
            let swapData
            if (content_type === CONTENT_TYPE.VIDEO) {
                swapData = {
                    content_type,
                    generation_id
                }
            } else {
                swapData = {
                    generation_id: generation_id,
                    source_indices: indices,
                    target_indices: indices
                }
            }

            setShowLoading(true)
            try {
                await swapFaceAsync(swapData, {
                    onSuccess: (data) => {
                        console.log('Face swap data:', data)
                        if (content_type === CONTENT_TYPE.IMAGE) {
                            dispatch(
                                updateGenerationSession({
                                    output_url: data.signed_swap_url
                                })
                            )
                        } else {
                            setShouldFetchVideoStatus(true)
                        }

                        navigate('/output')
                    }
                })
                console.log('Face swap successful')
            } catch (error) {
                console.error('Error swapping faces:', error)
                showError(
                    'Failed to perform face swap, try again with a new session'
                )
            }
            setShowLoading(false)
        } else {
            console.log('Not all images uploaded, cannot swap')
        }
    }

    // Function to upload all target images
    const uploadAllTargets = async () => {
        if (!generation_id) {
            console.error('No generation_id available')
            return
        }

        const allImagesUploaded =
            uploadedImages.length > 0 &&
            uploadedImages.every((image) => image !== null)

        if (!allImagesUploaded) {
            // showError("Please upload your image to begin face swap")
            throw new Error("Please upload your image to begin face swap")
        }

        const user_id =
            content_type === CONTENT_TYPE.VIDEO
                ? 'f519338d-442a-4613-af8d-04a02ef06fcd'
                : user.id

        console.log(
            'uploading target with generation_id:',
            generation_id,
            ' and user_id:',
            user_id
        )

        let fileKey = 'files'
        const formData = new FormData()
        if (content_type === CONTENT_TYPE.IMAGE) {
            formData.append('generation_id', generation_id)
            formData.append('user_id', user_id)
        } else {
            fileKey = 'file'
        }

        uploadedImages.forEach((file) => {
            if (file) {
                formData.append(`${fileKey}`, file)
            }
        })

        console.log(Object.fromEntries(formData))

        setShowLoading(true)
        try {
            if (content_type === CONTENT_TYPE.IMAGE) {
                await uploadTargetMutate({ formData, content_type })
            } else {
                await uploadTargetMutate({
                    generation_id,
                    group_id: '0',
                    formData,
                    content_type
                })
            }
            console.log('All targets uploaded successfully')
        } catch (error) {
            console.error('Error uploading targets:', error)
        }
        setShowLoading(false)
    }

    const handleImageUploadClick = (idx) => {
        setPendingUploadIdx(idx)
        setShowGuidelines(true)
    }

    const handleGuidelinesClose = () => {
        setShowGuidelines(false)
        // After closing guidelines, trigger file input click
        if (pendingUploadIdx !== null) {
            document.getElementById(`file-input-${pendingUploadIdx}`)?.click()
        }
    }

    // If no signed_template_url, show error state instead of navigating immediately
    if (!thumbnail_url) {
        return (
            <div className="min-h-screen bg-black flex flex-col items-center justify-center">
                <div className="text-center px-6">
                    <div className="text-white text-lg font-medium mb-4">
                        No template selected
                    </div>
                    <div className="text-[#bdbdbd] mb-6">
                        Please select a template to continue with face swap
                    </div>
                    <button
                        onClick={() => navigate(-1)}
                        className="bg-[#03586A] text-white rounded-full px-6 py-3 text-base font-semibold"
                    >
                        Go Back
                    </button>
                </div>
            </div>
        )
    }

    return (
        <div className="min-h-screen bg-black flex flex-col">
            {/* Header */}
            <div className="flex items-center px-4 pt-6 pb-2">
                <button
                    onClick={() => navigate(-1)}
                    className="text-white mr-3"
                >
                    <FaArrowLeft size={20} />
                </button>
                <span className="text-white text-lg font-medium">
                    Face Swap
                </span>
            </div>

            {/* Content */}
            <div className="flex-1 flex flex-col px-6">
                {/* Step 1 */}
                <div className="mt-2 mb-6">
                    <div className="text-xs text-[#bdbdbd] font-bold mb-1 text-start">
                        STEP 1
                    </div>
                    <div className="text-white font-medium mb-2 text-start">
                        Choose source
                    </div>
                    <SuggestedRailCard
                        item={{
                            template_id,
                            thumbnail: thumbnail_url,
                            filename: 'source image'
                        }}
                    />
                </div>

                {/* Step 2 */}
                <div className="mt-5 mb-8">
                    <div className="text-xs text-[#bdbdbd] font-bold mb-1 text-start">
                        STEP 2
                    </div>
                    <div className="text-white font-medium mb-4 text-start">
                        Choose target
                    </div>

                    {detected_face_urls?.length > 0 &&
                        detected_face_urls.map((detected_face_url, idx) => (
                            <div
                                className="flex items-center gap-4 mb-4"
                                key={idx}
                            >
                                <img
                                    src={detected_face_url}
                                    alt="Target"
                                    className="w-28 h-28 rounded-xl object-cover border border-gray-100 border-opacity-20"
                                />
                                <span className="text-[#bdbdbd]">
                                    <FaArrowRight />
                                </span>
                                <label className="relative w-28 h-28 rounded-xl bg-[#23243a] flex items-center justify-center cursor-pointer overflow-hidden">
                                    {uploadedImages[idx] ? (
                                        <img
                                            src={URL.createObjectURL(
                                                uploadedImages[idx]
                                            )}
                                            alt="Uploaded"
                                            className="w-full h-full object-cover"
                                        />
                                    ) : (
                                        <MdAddAPhoto
                                            className="text-[#bdbdbd]"
                                            size={28}
                                        />
                                    )}
                                    <input
                                        id={`file-input-${idx}`}
                                        type="file"
                                        accept="image/*"
                                        className="hidden"
                                        onChange={(e) =>
                                            handleImageUpload(
                                                e.target.files[0],
                                                idx
                                            )
                                        }
                                        onClick={(e) => e.stopPropagation()}
                                    />
                                    {/* Show guidelines modal before file input */}
                                    {!uploadedImages[idx] && (
                                        <div
                                            className="absolute inset-0 cursor-pointer"
                                            onClick={(e) => {
                                                e.preventDefault()
                                                handleImageUploadClick(idx)
                                            }}
                                        />
                                    )}
                                </label>
                            </div>
                        ))}
                </div>
            </div>

            {/* Button */}
            <div className="flex justify-center pb-8">
                <button
                    className="w-[90vw] max-w-md bg-[#03586A] text-white rounded-full py-3 text-base font-semibold shadow-md active:scale-95 transition"
                    onClick={handleFaceSwap}
                >
                    Swap Image 1 Credit
                </button>
            </div>

            <Loader show={showLoading} />

            <Bottomsheet
                open={showGuidelines}
                onClose={handleGuidelinesClose}
                title="Guidelines"
            >
                <ul className="text-white text-start text-sm list-disc pl-5 space-y-2">
                    <li>
                        Try to upload clear, well-lit photos without heavy blur
                        or pixelation.
                    </li>
                    <li>Only upload images you have the right to use.</li>
                    <li>
                        Avoid uploading offensive, violent, or adult content
                        (violates our community guidelines)
                    </li>
                    <li>
                        <span className="font-semibold">
                            Recommended Image Specs
                        </span>
                        :
                        <ul className="list-disc pl-5">
                            <li>Format: JPG or PNG</li>
                            <li>Size: Up to 10 MB</li>
                            <li>
                                Resolution: At least 512 x 512 pixels for best
                                results
                            </li>
                        </ul>
                    </li>
                </ul>
            </Bottomsheet>
        </div>
    )
}

export default Studio
