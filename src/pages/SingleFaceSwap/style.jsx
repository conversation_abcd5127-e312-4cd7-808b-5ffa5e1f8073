import styled from 'styled-components';

export const PageContainer = styled.div`
  display: flex;
  padding: 32px;
  background: #23243a;
  min-height: 100vh;
  color: #fff;
`;

export const GalleryContainer = styled.div`
  width: 220px;
  margin-right: 32px;
`;

export const GalleryTitle = styled.h3`
  margin-bottom: 16px;
`;

export const GalleryList = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
`;

export const GalleryImage = styled.img`
  width: 80px;
  height: 80px;
  border-radius: 12px;
  object-fit: cover;
  border: 2px solid #3ad1c7;
  cursor: pointer;
  box-shadow: ${({ selected }) => selected ? '0 0 0 3px #3ad1c7' : 'none'};
  background: #393a54;
`;

export const MainContent = styled.div`
  flex: 1;
  background: #2d2e44;
  border-radius: 16px;
  padding: 32px;
  min-height: 500px;
  display: flex;
  flex-direction: column;
`;

export const SectionTitle = styled.h2`
  margin-bottom: 24px;
`;

export const FlexRow = styled.div`
  display: flex;
  gap: 32px;
`;

export const SourceImageWrapper = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
`;

export const SourceImage = styled.img`
  width: 280px;
  height: 320px;
  object-fit: cover;
  border-radius: 16px;
  margin-bottom: 16px;
  background: #393a54;
`;

export const FaceOverlay = styled.div`
  position: absolute;
  border: 2px solid #3ad1c7;
  border-radius: 8px;
  width: 120px;
  height: 120px;
  top: 60px;
  left: 80px;
  pointer-events: none;
`;

export const UploadFaceSection = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
`;

export const UploadCircle = styled.div`
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: #393a54;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2.5em;
  color: #bdbdbd;
  cursor: pointer;
  box-sizing: border-box;
`;

export const SwapButton = styled.button`
  background: #3ad1c7;
  color: #23243a;
  border: none;
  border-radius: 8px;
  padding: 14px 36px;
  font-size: 1.1em;
  font-weight: 600;
  margin-top: 24px;
  cursor: pointer;
  transition: background 0.2s;
  &:disabled {
    background: #555;
    color: #aaa;
    cursor: not-allowed;
  }
`;

export const TermsRow = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 12px;
  font-size: 0.95em;
`;

export const OutputImage = styled.img`
  width: 280px;
  height: 320px;
  object-fit: cover;
  border-radius: 16px;
  margin-top: 16px;
  background: #393a54;
`;

export const CenteredModal = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(30, 32, 50, 0.85);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
`;

export const ModalContent = styled.div`
  background: #23243a;
  border-radius: 18px;
  padding: 36px 48px;
  color: #fff;
  min-width: 340px;
  text-align: center;
`;

export const ModalButton = styled.button`
  background: linear-gradient(90deg, #3ad1c7 0%, #2ec6ff 100%);
  color: #fff;
  border: none;
  border-radius: 8px;
  padding: 10px 32px;
  font-size: 1em;
  font-weight: 600;
  margin-top: 24px;
  cursor: pointer;
`; 