import {
    CenteredModal,
    FlexRow,
    GalleryContainer,
    GalleryImage,
    GalleryList,
    GalleryTitle,
    MainContent,
    ModalButton,
    ModalContent,
    PageContainer,
    SectionTitle,
    SourceImage,
    SwapButton,
    TermsRow,
    UploadCircle
} from './style'
import {
    Header,
    MobileHeader,
    MobileTabBar,
    PopularMoviesList,
    SuggestedList,
    TabBar,
    SuggestedRailCard
} from '../../components'
import { NOTE_TEXT, TAB_LABELS } from '../../utils/constants'
import React, { useEffect, useRef, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import {
    useFaceSwap,
    useGetAllFSTemplates,
    useGetFSTemplateInfo,
    useUploadFSTarget,
    useUploadFSTemplate
} from '../../Hooks/useFaceSwap'

import Loader from '../../components/UI/Loader'
import TempCard from './TempCard'
import { faceSwap } from '../../assets'
import { isMobile } from 'react-device-detect'
import { resetGenerationSession } from '../../redux/Reducers/generationSessionReducer'
import { setActiveTab } from '../../redux/Reducers/uiReducer'
import { useGetCreatePageData } from '../../Hooks/useCreatePage'

const SingleFaceSwap = () => {
    const dispatch = useDispatch()
    const user = useSelector((state) => state.auth.user)

    const [selectedTemplate, setSelectedTemplate] = useState(null)
    const [uploadedFace, setUploadedFace] = useState(null)
    const [uploadedFaceUrl, setUploadedFaceUrl] = useState(null)
    const [showNote, setShowNote] = useState(false)
    const [agreed, setAgreed] = useState(false)
    const [showResult, setShowResult] = useState(false)
    const [showLoading, setShowLoading] = useState(false)
    const [outputImage, setOutputImage] = useState(null)
    const [showTargetSuccessModal, setShowTargetSuccessModal] = useState(false)
    const fileInputRef = useRef()
    const [showImage, setShowImage] = useState(false)

    // react query implementation
    const {
        data: pageData,
        isLoading: isPageDataLoading,
        isSuccess: isPageDataSuccess,
        error: pageDataError
    } = useGetCreatePageData()
    const { data: RQtemplates, isLoading, isSuccess } = useGetAllFSTemplates()
    const { isLoading: RQtemplateInfoLoading } = useGetFSTemplateInfo({
        template_id: selectedTemplate?.template_id
    })
    const {
        mutate: uploadTemplateMutate,
        data: uploadTemplateData,
        isLoading: uploadTemplateLoading,
        isSuccess: uploadTemplateSuccess
    } = useUploadFSTemplate()
    const {
        mutateAsync: uploadTargetMutate,
        data: uploadTargetData,
        isLoading: uploadTargetLoading,
        isSuccess: uploadTargetSuccess
    } = useUploadFSTarget()
    const {
        mutateAsync: RQfaceSwapAsync,
        data: RQfaceSwapData,
        isLoading: RQfaceSwapLoading,
        error: RQfaceSwapError,
        isSuccess: RQfaceSwapSuccess
    } = useFaceSwap()

    useEffect(() => {
        console.log('RQfaceSwapData: ', RQfaceSwapData)
    }, [RQfaceSwapData])

    // show image after 2 sec
    useEffect(() => {
        if (selectedTemplate && uploadTemplateSuccess) {
            const timer = setTimeout(() => {
                setShowImage(true)
            }, 1000)

            return () => clearTimeout(timer)
        } else {
            setShowImage(false)
        }
    }, [uploadTemplateSuccess])

    const handleResetState = () => {
        setSelectedTemplate(null)
        setUploadedFace(null)
        setUploadedFaceUrl(null)
        setShowNote(false)
        setAgreed(false)
        setShowResult(false)
        setShowLoading(false)
        setOutputImage(null)
    }

    useEffect(() => {
        if (uploadTargetSuccess) {
            setShowTargetSuccessModal(true)
        }
    }, [uploadTargetSuccess])

    useEffect(() => {
        if (RQfaceSwapSuccess && RQfaceSwapData) {
            setOutputImage(
                RQfaceSwapData.signed_swap_url ||
                    RQfaceSwapData.output_url ||
                    RQfaceSwapData.result_url ||
                    RQfaceSwapData.image_url
            )
            setShowResult(true)
            setShowLoading(false)
        } else if (RQfaceSwapLoading) {
            setShowLoading(true)
        } else if (RQfaceSwapError) {
            setShowLoading(false)
            alert(
                'Face swap failed: ' + (RQfaceSwapError || 'Please try again.')
            )
        }
    }, [RQfaceSwapSuccess, RQfaceSwapData, RQfaceSwapError])

    const handleGallerySelect = (template) => {
        handleResetState()
        setSelectedTemplate(template)
        console.log('selected template: ', template)
        const formData = new FormData()
        formData.append('template_id', template.template_id)
        if (user && user.id) {
            formData.append('user_id', user.id)
        }
        uploadTemplateMutate(formData)
    }

    const handleUploadClick = () => {
        setShowNote(true)
    }

    const handleNoteAcknowledge = () => {
        setShowNote(false)
        fileInputRef.current.click()
    }

    const handleFileChange = async (e) => {
        const file = e.target.files[0]
        console.log('Selected file:', e)

        if (!file) return

        handleCloseResult()

        if (!uploadTemplateData || !uploadTemplateData?.generation_id) {
            alert('Please select a template from the gallery first.')
            return
        }

        if (
            !['image/jpeg', 'image/png'].includes(file.type) ||
            file.size > 5 * 1024 * 1024
        ) {
            alert('Invalid file. Please upload a JPG or PNG under 5MB.')
            return
        }
        const formData = new FormData()
        formData.append('generation_id', uploadTemplateData?.generation_id)
        formData.append('files', file)
        if (user && user.id) {
            formData.append('user_id', user.id)
        }

        await uploadTargetMutate(formData)
        setUploadedFace(file)
        setUploadedFaceUrl(URL.createObjectURL(file))
    }

    const handleSwap = async () => {
        if (!uploadTargetData || !uploadTargetData?.generation_id) {
            alert('Please upload a target face first')
            return
        }

        setShowLoading(true)

        const swapData = {
            generation_id: uploadTargetData?.generation_id,
            source_indices: [0],
            target_indices: [0]
        }

        await RQfaceSwapAsync(swapData)
    }

    const handleCloseResult = () => {
        setShowResult(false)
        setOutputImage(null)
    }

    useEffect(() => {
        dispatch(resetGenerationSession())
    }, [])

    if (isMobile) {
        return (
            <div className="bg-[#050505] min-h-screen text-white mb-20">
                <Header credits={4} />
                <div className="px-4 pt-5">
                    <div className="flex flex-col gap-2 mb-2">
                        <img
                            src={faceSwap}
                            alt="Face Swap"
                            className="w-8 h-8"
                        />
                        <div className="flex justify-between items-center">
                            <div className="text-3xl font-normal">
                                Face Swap
                            </div>
                            <a
                                href="#"
                                className="text-[#2ec6ff] text-sm ml-auto font-semibold"
                            >
                                How it works
                            </a>
                        </div>
                    </div>
                    <div className="text-start flex flex-col gap-2 my-6">
                        <div className="text-[#bdbdbd] text-xs font-medium">
                            STEP 1
                        </div>
                        <div className="text-base">
                            Choose a source photo or video and get started...
                        </div>
                    </div>

                    {/* REMOVE AFTER PROPER API IMTREGATION */}
                    <div className="mb-6">
                        <div className="text-start font-semibold text-xl mb-4">
                            Demo
                        </div>
                        <div className="flex gap-3 overflow-x-auto flex-nowrap hide-scrollbar">
                            {RQtemplates?.available_templates?.map((item) => (
                                <TempCard item={item} key={item.id} />
                            ))}
                        </div>
                    </div>

                    {pageData?.data &&
                        Object.entries(pageData.data).map(
                            ([sectionTitle, sectionData]) => {
                                const titleLower = sectionTitle.toLowerCase()

                                if (titleLower.includes('suggested')) {
                                    return (
                                        <SuggestedList
                                            key={sectionTitle}
                                            data={sectionData}
                                            title={sectionTitle}
                                        />
                                    )
                                } else if (titleLower.includes('popular')) {
                                    return (
                                        <PopularMoviesList
                                            key={sectionTitle}
                                            title={sectionTitle}
                                            data={sectionData}
                                        />
                                    )
                                }
                                return null
                            }
                        )}
                </div>
                <MobileTabBar />

                <Loader show={isPageDataLoading || isLoading} />
            </div>
        )
    }

    return (
        <div style={{ background: '#050505', minHeight: '100vh' }}>
            <Header credits={4} />
            <div
                style={{ maxWidth: 1400, margin: '0 auto', padding: '32px 0' }}
            >
                {/* Replace the tabs section with the TabBar component */}
                <TabBar />

                <PageContainer
                    style={{ boxShadow: '0 4px 32px #0002', borderRadius: 24 }}
                >
                    {/* Gallery */}
                    <GalleryContainer
                        style={{ padding: 18, minWidth: 220, maxHeight: 540 }}
                    >
                        <GalleryTitle
                            style={{ fontSize: 18, marginBottom: 18 }}
                        >
                            Choose From Gallery
                        </GalleryTitle>
                        <GalleryList>
                            {isLoading && <div>Loading...</div>}
                            {!isSuccess && <div>Failed to load gallery</div>}
                            {Array.isArray(RQtemplates.available_templates) &&
                                RQtemplates.available_templates.map(
                                    (template, idx) => (
                                        <GalleryImage
                                            key={template.template_id || idx}
                                            src={template.signed_template_url}
                                            alt={
                                                template.filename || 'template'
                                            }
                                            selected={
                                                selectedTemplate &&
                                                selectedTemplate.template_id ===
                                                    template.template_id
                                            }
                                            onClick={() =>
                                                handleGallerySelect(template)
                                            }
                                            style={{
                                                border:
                                                    selectedTemplate &&
                                                    selectedTemplate.template_id ===
                                                        template.template_id
                                                        ? '3px solid #3ad1c7'
                                                        : '2px solid #23243a',
                                                boxShadow:
                                                    selectedTemplate &&
                                                    selectedTemplate.template_id ===
                                                        template.template_id
                                                        ? '0 0 0 2px #2ec6ff'
                                                        : 'none',
                                                cursor: 'pointer',
                                                background: '#191a2a'
                                            }}
                                        />
                                    )
                                )}
                        </GalleryList>
                    </GalleryContainer>
                    {/* Main Content */}
                    <MainContent
                        style={{
                            background: '#23243a',
                            display: 'flex',
                            flexDirection: 'column',
                            justifyContent: 'space-between'
                        }}
                    >
                        <SectionTitle
                            style={{
                                fontWeight: 700,
                                fontSize: 26,
                                marginBottom: 18,
                                textAlign: 'center'
                            }}
                        >
                            ErosNow Photos & Video Face Swap AI
                        </SectionTitle>
                        <FlexRow
                            style={{
                                justifyContent: 'center',
                                alignItems: 'flex-start',
                                gap: 48
                            }}
                        >
                            {/* Source Image */}
                            <div
                                style={{
                                    flex: 1,
                                    display: 'flex',
                                    flexDirection: 'column',
                                    alignItems: 'center'
                                }}
                            >
                                <div
                                    style={{
                                        fontWeight: 600,
                                        fontSize: 18,
                                        marginBottom: 12
                                    }}
                                >
                                    Source Image
                                </div>
                                {selectedTemplate ? (
                                    <div
                                        style={{
                                            position: 'relative',
                                            width: 280,
                                            height: 320,
                                            borderRadius: 16,
                                            border: '0px solid #3ad1c7',
                                            boxShadow: '0 0 0 2px #2ec6ff'
                                        }}
                                    >
                                        <SourceImage
                                            src={
                                                selectedTemplate.signed_template_url
                                            }
                                            alt="Source"
                                            style={{
                                                width: '100%',
                                                height: '100%'
                                            }}
                                        />
                                    </div>
                                ) : (
                                    <div
                                        style={{
                                            width: 280,
                                            height: 320,
                                            background: '#393a54',
                                            borderRadius: 16,
                                            display: 'flex',
                                            alignItems: 'center',
                                            justifyContent: 'center',
                                            color: '#bdbdbd',
                                            border: '3px solid transparent'
                                        }}
                                    >
                                        Select a source image
                                    </div>
                                )}
                            </div>
                            {/* Arrow and Face Upload */}
                            <div
                                style={{
                                    flex: 1,
                                    display: 'flex',
                                    flexDirection: 'column',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    minWidth: 320
                                }}
                            >
                                <div
                                    style={{
                                        fontWeight: 600,
                                        fontSize: 18,
                                        marginBottom: 12
                                    }}
                                >
                                    Add your Face to Swap
                                </div>
                                <div
                                    style={{
                                        display: 'flex',
                                        alignItems: 'center',
                                        gap: 32,
                                        marginBottom: 18
                                    }}
                                >
                                    {/* Current face (circle) */}
                                    <div>
                                        {selectedTemplate ? (
                                            showImage ? (
                                                <img
                                                    src={
                                                        uploadTemplateData
                                                            ?.signed_detected_face_urls[0]
                                                    }
                                                    alt="Current Face"
                                                    style={{
                                                        width: 80,
                                                        height: 80,
                                                        borderRadius: '50%',
                                                        objectFit: 'cover',
                                                        border: '2px solid #3ad1c7',
                                                        background: '#191a2a'
                                                    }}
                                                />
                                            ) : (
                                                <UploadCircle
                                                    style={{
                                                        border: '2px dashed #3ad1c7'
                                                    }}
                                                >
                                                    ?
                                                </UploadCircle>
                                            )
                                        ) : (
                                            <UploadCircle
                                                style={{
                                                    border: '2px dashed #3ad1c7'
                                                }}
                                            ></UploadCircle>
                                        )}
                                    </div>
                                    <span
                                        style={{
                                            fontSize: 38,
                                            color: '#bdbdbd',
                                            fontWeight: 700
                                        }}
                                    >
                                        →
                                    </span>
                                    {/* Upload face (circle) */}
                                    <div>
                                        {uploadedFaceUrl ? (
                                            <img
                                                src={uploadedFaceUrl}
                                                alt="Uploaded Face"
                                                style={{
                                                    width: 80,
                                                    height: 80,
                                                    borderRadius: '50%',
                                                    objectFit: 'cover',
                                                    border: '2px solid #3ad1c7',
                                                    background: '#191a2a',
                                                    cursor: 'pointer',
                                                    boxSizing: 'border-box'
                                                }}
                                                onClick={() =>
                                                    fileInputRef.current.click()
                                                }
                                            />
                                        ) : (
                                            <UploadCircle
                                                onClick={handleUploadClick}
                                                style={{
                                                    border: '2px dashed #3ad1c7'
                                                }}
                                            >
                                                +
                                            </UploadCircle>
                                        )}
                                        <input
                                            type="file"
                                            accept="image/jpeg,image/png"
                                            style={{ display: 'none' }}
                                            ref={fileInputRef}
                                            onChange={handleFileChange}
                                        />
                                    </div>
                                </div>
                            </div>
                        </FlexRow>
                        {/* Terms and Swap Button */}
                        <div
                            style={{
                                display: 'flex',
                                flexDirection: 'column',
                                alignItems: 'center',
                                marginTop: 18
                            }}
                        >
                            <TermsRow>
                                <input
                                    type="checkbox"
                                    checked={agreed}
                                    onChange={(e) =>
                                        setAgreed(e.target.checked)
                                    }
                                />
                                <span
                                    style={{ color: '#bdbdbd', fontSize: 15 }}
                                >
                                    I agree to the{' '}
                                    <a href="#" style={{ color: '#3ad1c7' }}>
                                        Terms of use
                                    </a>{' '}
                                    &{' '}
                                    <a href="#" style={{ color: '#3ad1c7' }}>
                                        Privacy Policy
                                    </a>
                                </span>
                            </TermsRow>
                            <SwapButton
                                disabled={
                                    !selectedTemplate ||
                                    !uploadedFace ||
                                    !agreed ||
                                    RQfaceSwapLoading ||
                                    uploadTemplateLoading ||
                                    RQtemplateInfoLoading ||
                                    uploadTargetLoading
                                }
                                onClick={handleSwap}
                                style={{
                                    width: 260,
                                    marginTop: 18,
                                    fontSize: 18
                                }}
                            >
                                {uploadTemplateLoading ||
                                RQtemplateInfoLoading ||
                                uploadTargetLoading ||
                                RQfaceSwapLoading
                                    ? 'Processing...'
                                    : 'Swap Face Now'}
                            </SwapButton>
                            <div
                                style={{
                                    marginTop: 8,
                                    color: '#bdbdbd',
                                    fontSize: 15
                                }}
                            >
                                4 Creations Left
                            </div>
                        </div>
                    </MainContent>
                </PageContainer>
            </div>
            {/* Modals */}
            {showResult && outputImage && (
                <CenteredModal>
                    <ModalContent
                        style={{
                            background: '#23243a',
                            color: '#fff',
                            borderRadius: 16,
                            padding: '24px 32px',
                            maxWidth: 400
                        }}
                    >
                        <div
                            style={{
                                display: 'flex',
                                justifyContent: 'space-between',
                                alignItems: 'center'
                            }}
                        >
                            <h3 style={{ margin: 0, fontSize: 20 }}>
                                Share the photo with anyone you like!
                            </h3>
                            <button
                                onClick={handleCloseResult}
                                style={{
                                    background: 'none',
                                    border: 'none',
                                    color: '#fff',
                                    fontSize: 24,
                                    cursor: 'pointer'
                                }}
                            >
                                &times;
                            </button>
                        </div>
                        <div
                            style={{
                                margin: '24px 0',
                                display: 'flex',
                                justifyContent: 'center',
                                height: '48vh'
                            }}
                        >
                            <img
                                src={outputImage}
                                alt="Output"
                                style={{ maxWidth: '100%', borderRadius: 8 }}
                            />
                        </div>
                        <p
                            style={{
                                color: '#bdbdbd',
                                fontSize: 14,
                                textAlign: 'center',
                                margin: '0 0 24px 0'
                            }}
                        >
                            Warning: We do not save your avatar. Please download
                            and save it now, or your progress will be lost.
                        </p>
                        <div
                            style={{
                                display: 'flex',
                                justifyContent: 'center',
                                alignItems: 'center',
                                gap: 16
                            }}
                        >
                            <span style={{ fontSize: 16, fontWeight: 600 }}>
                                Share :
                            </span>
                            <div style={{ display: 'flex', gap: 12 }}>
                                {['f', 'X', 'S', 'I', 'W'].map((icon) => (
                                    <div
                                        key={icon}
                                        style={{
                                            width: 40,
                                            height: 40,
                                            borderRadius: '50%',
                                            background: '#393a54',
                                            display: 'flex',
                                            justifyContent: 'center',
                                            alignItems: 'center',
                                            cursor: 'pointer',
                                            fontWeight: 'bold'
                                        }}
                                    >
                                        {icon}
                                    </div>
                                ))}
                            </div>
                        </div>
                    </ModalContent>
                </CenteredModal>
            )}
            {showNote && (
                <CenteredModal>
                    <ModalContent>
                        <h3>Note</h3>
                        <ul
                            style={{
                                textAlign: 'left',
                                margin: '18px 0 0 0',
                                padding: 0,
                                listStyle: 'none',
                                color: '#bdbdbd'
                            }}
                        >
                            {NOTE_TEXT.map((t, i) => (
                                <li key={i} style={{ marginBottom: 8 }}>
                                    {t}
                                </li>
                            ))}
                        </ul>
                        <ModalButton onClick={handleNoteAcknowledge}>
                            I Understand
                        </ModalButton>
                    </ModalContent>
                </CenteredModal>
            )}
            {uploadTargetSuccess && showTargetSuccessModal && (
                <CenteredModal>
                    <ModalContent>
                        <div>Successfully uploaded</div>
                        <ModalButton
                            onClick={() => setShowTargetSuccessModal(false)}
                        >
                            Proceed
                        </ModalButton>
                    </ModalContent>
                </CenteredModal>
            )}
            {showLoading && (
                <CenteredModal>
                    <ModalContent>
                        <div style={{ fontSize: 22, marginBottom: 12 }}>
                            Generating...
                        </div>
                        <div style={{ fontSize: 48, color: '#3ad1c7' }}>
                            <span className="loader-dot">•</span>
                        </div>
                    </ModalContent>
                </CenteredModal>
            )}
            {(RQtemplateInfoLoading ||
                isLoading ||
                uploadTemplateLoading ||
                uploadTargetLoading) && (
                <CenteredModal>
                    <ModalContent>
                        <div style={{ fontSize: 22, marginBottom: 12 }}>
                            Loading...
                        </div>
                        <div style={{ fontSize: 48, color: '#3ad1c7' }}>
                            <span className="loader-dot">•</span>
                        </div>
                    </ModalContent>
                </CenteredModal>
            )}
        </div>
    )
}

export default SingleFaceSwap
