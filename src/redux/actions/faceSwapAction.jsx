import { createAsyncThunk } from '@reduxjs/toolkit'
import {
    fetchFaceSwapTemplates,
    swapFace,
    uploadFaceSwapTargets,
    uploadFaceSwapTemplate,
    fetchFaceSwapTemplateInfo
} from '../../api/faceSwapApi'

export const getTemplates = createAsyncThunk(
    'faceSwap/getTemplates',
    async (_, thunkAPI) => {
        try {
            const response = await fetchFaceSwapTemplates()
            return response
        } catch (error) {
            return thunkAPI.rejectWithValue(error.message)
        }
    }
)

export const getTemplateInfo = createAsyncThunk(
    'faceSwap/getTemplateInfo',
    async (templateId, thunkAPI) => {
        try {
            const response = await fetchFaceSwapTemplateInfo(templateId)
            return response
        } catch (error) {
            return thunkAPI.rejectWithValue(error.message)
        }
    }
)

export const uploadTargetFace = createAsyncThunk(
    'faceSwap/uploadTargetFace',
    async (formData, thunkAPI) => {
        try {
            const response = await uploadFaceSwapTargets(formData)
            return response
        } catch (error) {
            return thunkAPI.rejectWithValue(error.message)
        }
    }
)

export const doFaceSwap = createAsyncThunk(
    'faceSwap/doFaceSwap',
    async (swapData, thunkAPI) => {
        try {
            const response = await swapFace(swapData)
            return response
        } catch (error) {
            return thunkAPI.rejectWithValue(error.message)
        }
    }
)

export const uploadTemplate = createAsyncThunk(
    'faceSwap/uploadTemplate',
    async (formData, thunkAPI) => {
        try {
            const response = await uploadFaceSwapTemplate(formData)
            return response
        } catch (error) {
            return thunkAPI.rejectWithValue(error.message)
        }
    }
)
