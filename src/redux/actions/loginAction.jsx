import { loginAPI } from '../../api/loginApi'
import { createAsyncThunk } from '@reduxjs/toolkit'

export const login = createAsyncThunk(
    'auth/login',
    async ({ email, password }, thunkAPI) => {
        try {
            const response = await loginAPI(email, password)
            return response
        } catch (error) {
            return thunkAPI.rejectWithValue(error.message)
        }
    }
)
