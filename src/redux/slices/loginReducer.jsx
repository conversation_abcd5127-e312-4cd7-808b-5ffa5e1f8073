import { createSlice } from '@reduxjs/toolkit'
import { performLogin } from '../../api/loginApi'
import { createAsyncThunk } from '@reduxjs/toolkit'
// imp

export const login = createAsyncThunk(
    'auth/login',
    async ({ email, password }, thunkAPI) => {
        try {
            const response = await performLogin(email, password)
            return response
        } catch (error) {
            return thunkAPI.rejectWithValue(error.message)
        }
    }
)

const authSlice = createSlice({
    name: 'auth',
    initialState: {
        user: null,
        status: 'idle',
        error: null,
        // Always false on load for now
        isAuthenticated: false
    },
    reducers: {},
    extraReducers: (builder) => {
        builder
            .addCase(login.pending, (state) => {
                state.status = 'loading'
                state.error = null
            })
            .addCase(login.fulfilled, (state, action) => {
                state.status = 'succeeded'
                state.user = action.payload.user || action.payload
                state.isAuthenticated = true
            })
            .addCase(login.rejected, (state, action) => {
                state.status = 'failed'
                state.error = action.payload
                state.isAuthenticated = false
            })
    }
})

export default authSlice.reducer
