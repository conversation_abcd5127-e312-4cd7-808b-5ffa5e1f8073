import { createSlice, createAsyncThunk } from '@reduxjs/toolkit'
import { discoverApi } from '../../api/discoverApi'

export const discoverData = createAsyncThunk(
    'discover/data',
    async (thunkAPI) => {
        try {
            const response = await discover<PERSON>pi()
            return response
        } catch (error) {
            return thunkAPI.rejectWithValue(error.message)
        }
    }
)

const discoverSlice = createSlice({
    name: 'home',
    initialState: {
        status: 'idle',
        error: null
        // Always false on load for now
    },
    reducers: {},
    extraReducers: (builder) => {
        builder
            .addCase(discoverData.pending, (state) => {
                state.status = 'loading'
                state.error = null
            })
            .addCase(discoverData.fulfilled, (state, action) => {
                state.status = 'succeeded'
                state.user = action.payload
            })
            .addCase(discoverData.rejected, (state, action) => {
                state.status = 'failed'
                state.error = action.payload
            })
    }
})

export default discoverSlice.reducer
