import { createSlice } from '@reduxjs/toolkit'

const initialState = {
    template_id: null,
    content_type: null,
    thumbnail_url: null,
    file_url: null,
    generation_id: null,
    detected_face_urls: null,
    template_face_count: null,
    output_url: null
}

const generationSessionSlice = createSlice({
    name: 'generationSession',
    initialState,
    reducers: {
        updateGenerationSession: (state, action) => {
            // Merge the new data into the state
            Object.assign(state, action.payload)
        },
        resetGenerationSession: () => initialState
    }
})

export const { updateGenerationSession, resetGenerationSession } =
    generationSessionSlice.actions
export default generationSessionSlice.reducer
