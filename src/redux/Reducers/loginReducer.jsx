// src/features/auth/authSlice.js
import { createSlice, createAsyncThunk } from '@reduxjs/toolkit'
import { loginAPI } from '../../api/loginApi'

export const login = createAsyncThunk(
    'auth/login',
    async ({ email, password }, thunkAPI) => {
        try {
            const response = await loginAPI(email, password)
            return response
        } catch (error) {
            let message = typeof error === 'string'
                ? error
                : error?.detail || error?.message || JSON.stringify(error);
            return thunkAPI.rejectWithValue(message);
        }
    }
)

const authSlice = createSlice({
    name: 'auth',
    initialState: {
        user: null,
        status: 'idle',
        error: null,
        // Always false on load for now
        isAuthenticated: false
    },
    reducers: {
        clearError(state) {
            state.error = null;
        }
    },
    extraReducers: (builder) => {
        builder
            .addCase(login.pending, (state) => {
                state.status = 'loading'
                state.error = null
            })
            .addCase(login.fulfilled, (state, action) => {
                state.status = 'succeeded'
                state.user = action.payload.user || action.payload
                state.isAuthenticated = true
            })
            .addCase(login.rejected, (state, action) => {
                state.status = 'failed'
                state.error = action.payload
                state.isAuthenticated = false
            })
    }
})

export const { clearError } = authSlice.actions;

export default authSlice.reducer
