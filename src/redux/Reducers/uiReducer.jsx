import { createSlice } from '@reduxjs/toolkit'
import { TAB_LABELS } from '../../utils/constants'

const uiSlice = createSlice({
    name: 'ui',
    initialState: {
        activeTab: TAB_LABELS.PHOTO
    },
    reducers: {
        setActiveTab: (state, action) => {
            state.activeTab = action.payload
        }
    }
})

export const { setActiveTab } = uiSlice.actions
export default uiSlice.reducer
