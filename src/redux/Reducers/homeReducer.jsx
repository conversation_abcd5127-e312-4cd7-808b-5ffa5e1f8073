import { createSlice, createAsyncThunk } from '@reduxjs/toolkit'
import { homeApi } from '../../api/homeApi'

export const homeData = createAsyncThunk('home/data', async (thunkAPI) => {
    try {
        const response = await homeApi()
        return response
    } catch (error) {
        return thunkAPI.rejectWithValue(error.message)
    }
})

const homeSlice = createSlice({
    name: 'home',
    initialState: {
        status: 'idle',
        error: null
        // Always false on load for now
    },
    reducers: {},
    extraReducers: (builder) => {
        builder
            .addCase(homeData.pending, (state) => {
                state.status = 'loading'
                state.error = null
            })
            .addCase(homeData.fulfilled, (state, action) => {
                state.status = 'succeeded'
                state.user = action.payload
            })
            .addCase(homeData.rejected, (state, action) => {
                state.status = 'failed'
                state.error = action.payload
            })
    }
})

export default homeSlice.reducer
