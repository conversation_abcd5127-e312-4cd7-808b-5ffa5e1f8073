import { createSlice, createAsyncThunk } from '@reduxjs/toolkit'
import {
    fetchFaceSwapTemplates,
    swapFace,
    uploadFaceSwapTargets,
    uploadFaceSwapTemplate,
    fetchFaceSwapTemplateInfo
} from '../../api/faceSwapApi'

export const getTemplates = createAsyncThunk(
    'faceSwap/getTemplates',
    async (_, thunkAPI) => {
        try {
            const response = await fetchFaceSwapTemplates()
            return response
        } catch (error) {
            return thunkAPI.rejectWithValue(error.message)
        }
    }
)

export const getTemplateInfo = createAsyncThunk(
    'faceSwap/getTemplateInfo',
    async (templateId, thunkAPI) => {
        try {
            const response = await fetchFaceSwapTemplateInfo(templateId)
            return response
        } catch (error) {
            return thunkAPI.rejectWithValue(error.message)
        }
    }
)

export const uploadTargetFace = createAsyncThunk(
    'faceSwap/uploadTargetFace',
    async (formData, thunkAPI) => {
        try {
            const response = await uploadFaceSwapTargets(formData)
            return response
        } catch (error) {
            return thunkAPI.rejectWithValue(error.message)
        }
    }
)

export const doFaceSwap = createAsyncThunk(
    'faceSwap/doFaceSwap',
    async (swapData, thunkAPI) => {
        try {
            const response = await swapFace(swapData)
            return response
        } catch (error) {
            return thunkAPI.rejectWithValue(error.message)
        }
    }
)

export const uploadTemplate = createAsyncThunk(
    'faceSwap/uploadTemplate',
    async (formData, thunkAPI) => {
        try {
            const response = await uploadFaceSwapTemplate(formData)
            return response
        } catch (error) {
            return thunkAPI.rejectWithValue(error.message)
        }
    }
)

const faceSwapSlice = createSlice({
    name: 'faceSwap',
    initialState: {
        templates: [],
        templatesStatus: 'idle',
        templatesError: null,
        templateInfo: null,
        templateInfoStatus: 'idle',
        templateInfoError: null,
        swapResult: null,
        swapStatus: 'idle',
        swapError: null,
        uploadStatus: 'idle',
        uploadResult: null,
        uploadError: null,
        templateUploadStatus: 'idle',
        templateUploadResult: null,
        templateUploadError: null
    },
    reducers: {
        clearAll: (state) => {
            state.templates = []
            state.templatesStatus = 'idle'
            state.templatesError = null
            state.templateInfo = null
            state.templateInfoStatus = 'idle'
            state.templateInfoError = null
            state.swapResult = null
            state.swapStatus = 'idle'
            state.swapError = null
            state.uploadStatus = 'idle'
            state.uploadResult = null
            state.uploadError = null
            state.templateUploadStatus = 'idle'
            state.templateUploadResult = null
            state.templateUploadError = null
        },
        clearSwapResult: (state) => {
            state.swapResult = null
            state.swapStatus = 'idle'
            state.swapError = null
        },
        clearUploadResult: (state) => {
            state.uploadResult = null
            state.uploadStatus = 'idle'
            state.uploadError = null
        },
        clearTemplateUploadResult: (state) => {
            state.templateUploadResult = null
            state.templateUploadStatus = 'idle'
            state.templateUploadError = null
        },
        clearTemplateInfo: (state) => {
            state.templateInfo = null
            state.templateInfoStatus = 'idle'
            state.templateInfoError = null
        }
    },
    extraReducers: (builder) => {
        builder
            .addCase(getTemplates.pending, (state) => {
                state.templatesStatus = 'loading'
                state.templatesError = null
            })
            .addCase(getTemplates.fulfilled, (state, action) => {
                state.templatesStatus = 'succeeded'
                console.log('Gallery API response:', action.payload)
                if (Array.isArray(action.payload)) {
                    state.templates = action.payload
                } else if (
                    action.payload &&
                    Array.isArray(action.payload.available_templates)
                ) {
                    state.templates = action.payload.available_templates
                } else if (
                    action.payload &&
                    Array.isArray(action.payload.templates)
                ) {
                    state.templates = action.payload.templates
                } else {
                    state.templates = []
                }
            })
            .addCase(getTemplates.rejected, (state, action) => {
                state.templatesStatus = 'failed'
                state.templatesError = action.payload
            })
            .addCase(getTemplateInfo.pending, (state) => {
                state.templateInfoStatus = 'loading'
                state.templateInfoError = null
            })
            .addCase(getTemplateInfo.fulfilled, (state, action) => {
                state.templateInfoStatus = 'succeeded'
                state.templateInfo = action.payload
            })
            .addCase(getTemplateInfo.rejected, (state, action) => {
                state.templateInfoStatus = 'failed'
                state.templateInfoError = action.payload
            })
            .addCase(uploadTargetFace.pending, (state) => {
                state.uploadStatus = 'loading'
                state.uploadError = null
            })
            .addCase(uploadTargetFace.fulfilled, (state, action) => {
                state.uploadStatus = 'succeeded'
                state.uploadResult = action.payload
            })
            .addCase(uploadTargetFace.rejected, (state, action) => {
                state.uploadStatus = 'failed'
                state.uploadError = action.payload
            })
            .addCase(doFaceSwap.pending, (state) => {
                state.swapStatus = 'loading'
                state.swapError = null
            })
            .addCase(doFaceSwap.fulfilled, (state, action) => {
                state.swapStatus = 'succeeded'
                state.swapResult = action.payload
            })
            .addCase(doFaceSwap.rejected, (state, action) => {
                state.swapStatus = 'failed'
                state.swapError = action.payload
            })
            .addCase(uploadTemplate.pending, (state) => {
                state.templateUploadStatus = 'loading'
                state.templateUploadError = null
            })
            .addCase(uploadTemplate.fulfilled, (state, action) => {
                state.templateUploadStatus = 'succeeded'
                state.templateUploadResult = action.payload
            })
            .addCase(uploadTemplate.rejected, (state, action) => {
                state.templateUploadStatus = 'failed'
                state.templateUploadError = action.payload
            })
    }
})

export const {
    clearAll,
    clearSwapResult,
    clearUploadResult,
    clearTemplateUploadResult,
    clearTemplateInfo
} = faceSwapSlice.actions
export default faceSwapSlice.reducer
