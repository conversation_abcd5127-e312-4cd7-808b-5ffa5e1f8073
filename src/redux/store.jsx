import { configureStore } from '@reduxjs/toolkit'
import faceSwapReducer from './Reducers/faceSwapReducer'
import loginReducer from './Reducers/loginReducer'
import sessionReducer from './Reducers/generationSessionReducer'
import uiReducer from './Reducers/uiReducer'
import homeReducer from './Reducers/homeReducer'
import discoverReducer from './Reducers/discoverReducer'

const store = configureStore({
    reducer: {
        auth: loginReducer,
        faceSwap: faceSwapReducer,
        ui: uiReducer,
        session: sessionReducer,
        homeData: homeReducer,
        discoverData: discoverReducer
    }
})

export default store
