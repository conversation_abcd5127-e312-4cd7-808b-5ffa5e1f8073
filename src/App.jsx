import './App.css'

import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import React, { useEffect, useState } from 'react'
import { ScrollToTop, Splash } from './components'

import AppRoutes from './routes/appRoutes'
import { BrowserRouter as Router } from 'react-router-dom'
import Theme from './Theme'
import { ToastProvider } from './components/UI'

// import a from './datalayer'
const queryClient = new QueryClient()
function App() {
    const [showSplash, setShowSplash] = useState(true)
    // console.log(
    //     '************',
    //     a.com.eros.shared.signUp(
    //         'demo',
    //         '<EMAIL>',
    //         '**********',
    //         '123',
    //         'active',
    //         'user'
    //     )
    // )

    return (
        <Theme>
            <QueryClientProvider client={queryClient}>
                <ToastProvider>
                    <Router>
                        <div className="App">
                            {showSplash ? (
                                <>
                                    <Splash onEnter={() => setShowSplash(false)} />
                                </>
                            ) : (
                                <ScrollToTop>
                                    <AppRoutes />
                                </ScrollToTop>
                            )}
                        </div>
                    </Router>
                </ToastProvider>
            </QueryClientProvider>
        </Theme>
    )
}

export default App
