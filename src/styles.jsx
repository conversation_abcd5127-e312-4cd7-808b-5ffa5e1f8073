import { createGlobalStyle } from 'styled-components'

const GlobalStyles = createGlobalStyle`
  body {
    // font-family: ${({ theme }) => theme.fonts.fontFaces};
    font-family: 'Poppins', sans-serif;
    margin: 0;
    padding: 0;
    background-color: ${({ theme }) => theme.colors.Black};
    color: ${({ theme }) => theme.colors.White};
    overflow: 'hidden'
  }
  @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap');
`

export default GlobalStyles
