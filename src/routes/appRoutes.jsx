import {
    DoubleFaceSwap,
    Home,
    Login,
    Output,
    PhotoGallery,
    Player,
    Register,
    SelectSource,
    Settings,
    SingleFaceSwap,
    Studio,
    VideoFaceSwap
} from '../pages'
import { Route, Routes } from 'react-router-dom'

import { useSelector } from 'react-redux'

const AppRoutes = () => {
    const isAuthenticated = useSelector((state) => state.auth.isAuthenticated)
    return (
        <Routes>
            <Route path="/" element={isAuthenticated ? <Home /> : <Login />} />
            <Route path="/login" element={<Login />} />
            <Route path="/home" element={<Home />} />
            <Route path="/register" element={<Register />} />
            {/* <Route path="/double-face-swap" element={<DoubleFaceSwap />} /> */}
            <Route path="/video-face-swap" element={<VideoFaceSwap />} />
            <Route path="/player" element={<Player />} />

            <Route path="/create" element={<SingleFaceSwap />} />
            <Route path="/setting" element={<Settings />} />

            {/* video face swap routes */}

            {/* face swap routes */}
            <Route path="/source" element={<SelectSource />} />
            <Route path="/studio" element={<Studio />} />
            <Route path="/gallery/:query" element={<PhotoGallery />} />
            <Route path="/output" element={<Output />} />
        </Routes>
    )
}

export default AppRoutes
