/* eslint-disable no-unused-vars */

const NOTE_TEXT = [
    '1. The user must upload a genuine image.',
    '2. Uploading unauthorised images is strictly prohibited.',
    '3. The uploaded image must display the full face to ensure it can be swapped correctly.',
    '4. Image Size should be within 5 MB',
    '5. Image Formats should be JPG, PNG'
]

const TAB_LABELS = {
    PHOTO: 'Photo Face Swap',
    MULTIPLE: 'Multiple Face Swap',
    VIDEO: 'Video Face Swap'
}

const TABS = [
    { label: TAB_LABELS.PHOTO, active: true, path: '/single-face-swap' },
    { label: TAB_LABELS.MULTIPLE, active: false, path: '/double-face-swap' },
    { label: TAB_LABELS.VIDEO, active: false, path: '/video-face-swap' }
]

const CONTENT_TYPE = {
    IMAGE: 'image_face_swap',
    VIDEO: 'video_face_swap'
}

export { NOTE_TEXT, TAB_LABELS, TABS, CONTENT_TYPE }
