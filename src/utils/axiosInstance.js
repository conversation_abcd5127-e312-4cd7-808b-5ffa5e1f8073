import axios from 'axios'
import kmpApi from '../datalayer'

const api = axios.create({
    baseURL: 'http://14.195.114.66:8000/api/',
    headers: {
        'Content-Type': 'application/json'
    }
})

api.interceptors.request.use(
    (config) => {
        const accessToken = localStorage.getItem('access') // <- make sure this matches what you store
        if (accessToken) {
            config.headers['Authorization'] = `Bearer ${accessToken}`
        }
        return config
    },
    (error) => Promise.reject(error)
)

let isRefreshing = false
let failedQueue = []

const processQueue = (error, token = null) => {
    failedQueue.forEach((prom) => {
        if (error) {
            prom.reject(error)
        } else {
            prom.resolve(token)
        }
    })
    failedQueue = []
}

api.interceptors.response.use(
    (response) => response,
    async (error) => {
        const originalRequest = error.config

        if (error.response?.status === 401 && !originalRequest._retry) {
            originalRequest._retry = true

            if (isRefreshing) {
                return new Promise(function (resolve, reject) {
                    failedQueue.push({ resolve, reject })
                })
                    .then((token) => {
                        originalRequest.headers['Authorization'] =
                            'Bearer ' + token
                        return api(originalRequest)
                    })
                    .catch((err) => Promise.reject(err))
            }

            isRefreshing = true

            const refreshToken = localStorage.getItem('refresh')

            try {
                const response = await axios.post(
                    'http://14.195.114.66:8000/api/token/refresh/',
                    { refresh: refreshToken }
                )

                const newAccessToken = response.data.access_token
                localStorage.setItem('access', newAccessToken) // must match request interceptor

                api.defaults.headers.common['Authorization'] =
                    'Bearer ' + newAccessToken
                processQueue(null, newAccessToken)

                originalRequest.headers['Authorization'] =
                    'Bearer ' + newAccessToken
                return api(originalRequest)
            } catch (err) {
                processQueue(err, null)
                return Promise.reject(err)
            } finally {
                isRefreshing = false
            }
        }

        return Promise.reject(error)
    }
)

// Response interceptor for error handling
// api.interceptors.response.use(
//     (response) => response,
//     (error) => {
//         if (error.response && error.response.status === 401) {
//             // TODO: Handle token refresh or logout
//             // For now, just clear tokens and reload
//             localStorage.removeItem('access')
//             localStorage.removeItem('refresh')
//             localStorage.removeItem('user')
//             // window.location.reload(); // Optionally force logout
//         }
//         return Promise.reject(error)
//     }
// )

const faceSwapApi = axios.create({
    baseURL:
        'http://image-face-swap.runai-project-immerso-innnovation-venture-pvt.inferencing.shakticloud.ai',
    headers: {
        'Content-Type': 'application/json'
    }
})

faceSwapApi.interceptors.response.use(
    (response) => response,
    (error) => {
        if (error.response && error.response.status === 422) {
            window.location.href = '/'
        }
        return Promise.reject(error)
    }
)
 

const videoFaceSwapApi = axios.create({
    baseURL:
        'http://video-face-swap.runai-project-immerso-innnovation-venture-pvt.inferencing.shakticloud.ai'
})

const KMP_API_Variable = kmpApi.com.eros.shared
export { api, faceSwapApi, videoFaceSwapApi, KMP_API_Variable }
