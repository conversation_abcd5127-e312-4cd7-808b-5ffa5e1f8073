import { api, faceSwapApi, videoFaceSwapApi } from '../utils/axiosInstance'

/**
 * @typedef {Object} ApiRequestConfig
 * @property {'get' | 'post' | 'put' | 'patch' | 'delete'} [method] - HTTP method.
 * @property {string} url - API endpoint.
 * @property {Object} [data] - Request body (for POST/PUT).
 * @property {Object} [params] - URL query parameters.
 * @property {Object} [headers] - Custom headers.
 */

/**
 * Makes an API request using the main axios instance.
 * @param {ApiRequestConfig} config - The request configuration.
 * @returns {Promise<any>} - API response data.
 */
export const apiRequest = async ({ method, url, data, params, headers }) => {
    try {
        const response = await api.request({
            method: method || 'get',
            url,
            data,
            params,
            headers: headers || {
                'Content-Type': 'application/json'
            }
        })
        return response.data
    } catch (error) {
        console.error('API Request Error:', error)

        if (error?.response?.data) {

            if (typeof error.response.data === 'object' && error.response.data.detail) {
                throw new Error(error.response.data.detail)
            }
            if (typeof error.response.data === 'string') {
                throw new Error(error.response.data)
            }
            throw new Error(JSON.stringify(error.response.data))
        }

        throw new Error(error.message || 'An unexpected error occurred')
    }
}

/**
 * Makes an API request to the Face Swap service.
 * @param {ApiRequestConfig} config - The request configuration.
 * @returns {Promise<any>} - API response data.
 */
export const faceSwapApiRequest = async ({
    method,
    url,
    data,
    params,
    headers
}) => {
    try {
        const response = await faceSwapApi.request({
            method: method || 'post',
            url,
            data,
            params,
            headers: headers || {
                'Content-Type': 'application/json'
            }
        })
        return response.data
    } catch (error) {
        console.error('Face Swap API Request Error:', error)

        if (error?.response?.data) {
            if (typeof error.response.data === 'object' && error.response.data.detail) {
                throw new Error(error.response.data.detail)
            }
            if (typeof error.response.data === 'string') {
                throw new Error(error.response.data)
            }
            throw new Error(JSON.stringify(error.response.data))
        }

        throw new Error(error.message || 'Face swap request failed')
    }
}

/**
 * Makes an API request to the Video Face Swap service.
 * @param {ApiRequestConfig} config - The request configuration.
 * @returns {Promise<any>} - API response data.
 */
export const videoFaceSwapApiRequest = async ({
    method,
    url,
    data,
    params,
    headers
}) => {
    try {
        const response = await videoFaceSwapApi.request({
            method: method || 'post',
            url,
            data,
            params,
            headers: headers || {
                'Content-Type': 'application/json'
            }
        })
        return response.data
    } catch (error) {
        console.error('Video Face Swap API Request Error:', error?.detail ?? error)

        if (error?.response?.data) {

            if (typeof error.response.data === 'object' && error.response.data.detail) {
                throw new Error(error.response.data.detail)
            }

            if (typeof error.response.data === 'string') {
                throw new Error(error.response.data)
            }

            throw new Error(JSON.stringify(error.response.data))
        }

        throw new Error(error.message || 'Video face swap request failed')
    }
}
