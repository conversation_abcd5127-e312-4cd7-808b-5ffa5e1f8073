import { faceSwapApiRequest, videoFaceSwapApiRequest } from './apiWrapper'

export async function fetchFaceSwapTemplates() {
    const response = await faceSwapApiRequest({
        method: 'get',
        url: '/templates'
    })
    return response
}

export async function fetchFaceSwapTemplateInfo(template_id) {
    const response = await faceSwapApiRequest({
        method: 'get',
        url: `/templates/${template_id}/info`
    })
    return response
}

export async function uploadFaceSwapTemplate(formData) {
    const response = await faceSwapApiRequest({
        method: 'post',
        url: '/upload_template',
        data: formData,
        headers: {
            'Content-Type': 'multipart/form-data'
        }
    })
    return response
}

export async function uploadFaceSwapTargets(formData) {
    const response = await faceSwapApiRequest({
        method: 'post',
        url: '/upload_targets',
        data: formData,
        headers: {
            'Content-Type': 'multipart/form-data'
        }
    })
    return response
}

/**
 * Performs face swap operation. Use target_indices to control single or multi-face swap:
 * - For single face: target_indices = [0]
 * - For multiple faces: target_indices = [0, 1, ...] (array of indices to swap)
 */
export async function swapFace({
    generation_id,
    source_indices,
    target_indices,
    ...otherData
}) {
    const response = await faceSwapApiRequest({
        method: 'post',
        url: '/swap_face',
        data: {
            generation_id,
            source_indices,
            target_indices: Array.isArray(target_indices)
                ? target_indices
                : [0], // Default to [0] if not provided
            ...otherData
        },
        headers: {
            'Content-Type': 'application/json'
        }
    })
    return response
}

export async function fetchFSVideoStatus(generation_id) {
    const response = await videoFaceSwapApiRequest({
        method: 'get',
        url: `/faceswap/status/${generation_id}`
    })
    return response
}