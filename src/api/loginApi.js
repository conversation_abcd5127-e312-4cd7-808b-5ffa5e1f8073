import { KMP_API_Variable } from '../utils/axiosInstance'
import { apiRequest } from './apiWrapper'

export async function performLogin(email, password) {
    const response = await KMP_API_Variable.login(email, password)
    console.log('Login successful:', response)
    return response
}

export async function performSignUp({
    username,
    email,
    contact_number,
    password,
    account_status = 'active',
    role = 'user'
}) {
    const response = await KMP_API_Variable.signUp(
        username,
        email,
        contact_number,
        password,
        account_status,
        role
    )
    console.log('Sign up successful:', response)
    return response
}

export async function loginAPI(email, password) {
    const response = await apiRequest({
        method: 'post',
        url: 'login/',
        data: {
            email,
            password
        }
    })
    return response
}

export async function registerAPI({
    username,
    email,
    contact_number,
    password,
    account_status = 'active',
    role = 'user'
}) {
    const response = await apiRequest({
        method: 'post',
        url: 'user/',
        data: {
            username,
            email,
            contact_number,
            password,
            account_status,
            role
        }
    })
    return response
}

export async function refreshToken(refresh) {
    const response = await apiRequest({
        method: 'post',
        url: 'token/refresh/',
        data: {
            refresh
        }
    })
    return response
}
