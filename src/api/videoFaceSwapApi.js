// import { videoFaceSwapApi } from '../Utils/axiosInstance';
import { videoFaceSwapApiRequest } from './apiWrapper'

export async function fetchFaceSwapVideoInfo(template_id) {
    const response = await videoFaceSwapApiRequest({
        method: 'get',
        url: `/templates/${template_id}/info`,
    })

    return response;
}

export async function uploadVideoForFaceSwap(formData) {
    const response = await videoFaceSwapApiRequest({
        method: 'post',
        url: '/uploadvideo/',
        data: formData,
        headers: {
            'Content-Type': 'multipart/form-data'
        }
    })
    return response
}

export const uploadNewFace = async (generationId, groupId, formData) => {
    const response = await videoFaceSwapApiRequest({
        method: 'post',
        url: `/uploadnewfaces/${generationId}/${groupId}`,
        data: formData,
        headers: {
            'Content-Type': 'multipart/form-data'
        }
    })
    return response
}

export const startFaceSwap = async (generationId) => {
    const response = await videoFaceSwapApiRequest({
        method: 'post',
        url: `/faceswap/${generationId}`,
        data: {
            group_ids: ['0']
        }
    })
    return response
}

export const getFaceSwapStatus = async (generationId) => {
    const response = await videoFaceSwapApiRequest({
        method: 'get',
        url: `/faceswap/status/${generationId}`
    })
    return response
}
